package com.pmw790.power.schema;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents a collection of binding patterns for a specific block type.
 * Each schema is associated with a specific owner block (e.g., "Power Provider")
 * and contains all the binding patterns for that block.
 */
public class BindingSchema {
    // The name of the owner block (e.g., "Power Provider")
    private final String ownerName;

    // List of all binding patterns for this block type
    private final List<BindingPattern> patterns;

    // Maps for efficient lookup of patterns
    private final Map<String, List<BindingPattern>> patternsBySourceProperty;
    private final Map<String, List<BindingPattern>> patternsByTargetConstraint;
    private final Map<String, List<BindingPattern>> patternsBySourceConstraint;
    private final Map<String, List<BindingPattern>> patternsByTargetProperty;

    /**
     * Constructor for BindingSchema
     *
     * @param ownerName The name of the owner block (e.g., "Power Provider")
     */
    public BindingSchema(String ownerName) {
        this.ownerName = ownerName;
        this.patterns = new ArrayList<>();
        this.patternsBySourceProperty = new HashMap<>();
        this.patternsByTargetConstraint = new HashMap<>();
        this.patternsBySourceConstraint = new HashMap<>();
        this.patternsByTargetProperty = new HashMap<>();
    }

    /**
     * Gets all patterns in this schema
     *
     * @return List of all binding patterns
     */
    public List<BindingPattern> getPatterns() {
        return new ArrayList<>(patterns);
    }

    // -------------------------------------------------------------------------
    // HELPERS METHODS
    // -------------------------------------------------------------------------

    /**
     * Adds a pattern to all relevant indexes
     *
     * @param pattern The pattern to add
     */
    public void addPatternToIndexes(BindingPattern pattern) {
        // Add to the main list
        patterns.add(pattern);

        if (pattern.isConstraintToConstraint()) {
            // Index by source constraint
            String sourceConstraint = pattern.getSourceConstraint();
            if (!patternsBySourceConstraint.containsKey(sourceConstraint)) {
                patternsBySourceConstraint.put(sourceConstraint, new ArrayList<>());
            }
            patternsBySourceConstraint.get(sourceConstraint).add(pattern);
        } else {
            // Index by source property
            String sourceProperty = pattern.getSourceProperty();
            if (!patternsBySourceProperty.containsKey(sourceProperty)) {
                patternsBySourceProperty.put(sourceProperty, new ArrayList<>());
            }
            patternsBySourceProperty.get(sourceProperty).add(pattern);
        }

        // Index by target property
        if (pattern.hasTargetProperty()) {
            String targetProperty = pattern.getTargetProperty();
            if (!patternsByTargetProperty.containsKey(targetProperty)) {
                patternsByTargetProperty.put(targetProperty, new ArrayList<>());
            }
            patternsByTargetProperty.get(targetProperty).add(pattern);
        } else {
            // Index by target constraint
            String targetConstraint = pattern.getTargetConstraint();
            if (!patternsByTargetConstraint.containsKey(targetConstraint)) {
                patternsByTargetConstraint.put(targetConstraint, new ArrayList<>());
            }
            patternsByTargetConstraint.get(targetConstraint).add(pattern);
        }
    }

    /**
     * Gets the number of patterns in this schema
     *
     * @return The number of patterns
     */
    public int getPatternCount() {
        return patterns.size();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("BindingSchema for ").append(ownerName).append(" with ")
          .append(patterns.size()).append(" patterns:\n");

        for (BindingPattern pattern : patterns) {
            sb.append("  ").append(pattern.toString()).append("\n");
        }

        return sb.toString();
    }
}