//package com.pmw790.power.functions;
//
//import com.nomagic.magicdraw.core.Application;
//import com.nomagic.magicdraw.core.Project;
//import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
//import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.NamedElement;
//
//import javax.swing.*;
//import javax.swing.table.DefaultTableModel;
//import javax.swing.table.TableRowSorter;
//import java.awt.*;
//import java.util.List;
//import java.util.Map;
//import java.util.ArrayList;
//import java.util.List;
//
//public class PowerTableManager {
//    private static JDialog currentDialog = null;
//    private static boolean isTableActive = false;
//
//    public static void closeTable() {
//        if (isTableActive && currentDialog != null && currentDialog.isDisplayable()) {
//            currentDialog.dispose();
//            isTableActive = false;
//            currentDialog = null;
//        }
//    }
//
//    public static void createPowerTable() {
//        closeTable();
//
//        SwingUtilities.invokeLater(() -> {
//            try {
//                isTableActive = true;
//                Project project = SysMLStereotypes.getProject();
//
//                // Create a non-editable table model
//                DefaultTableModel model = new DefaultTableModel() {
//                    @Override
//                    public boolean isCellEditable(int row, int column) {
//                        return false;
//                    }
//
//                    @Override
//                    public Class<?> getColumnClass(int columnIndex) {
//                        if (columnIndex == 2) return Double.class; // Power column as Double
//                        return String.class;
//                    }
//                };
//
//                // Add columns
//                model.addColumn("Asset Name");
//                model.addColumn("Type");
////                model.addColumn("Power (W)");
//
//                // Create and configure table
//                JTable table = new JTable(model);
//                table.setFillsViewportHeight(true);
//                table.setRowHeight(25);
//                table.getTableHeader().setReorderingAllowed(false);
//
//                // Add row sorter
//                TableRowSorter<DefaultTableModel> sorter = new TableRowSorter<>(model);
//                table.setRowSorter(sorter);
//
//                // Populate with data
//                populateTableWithModelData(model, project);
//
//                // Create scroll pane and frame
//                JScrollPane scrollPane = new JScrollPane(table);
//
//                // Create dialog
//                JFrame mainFrame = Application.getInstance().getMainFrame();
//                JDialog dialog = new JDialog(mainFrame, "Power Analysis Table", false);
//                currentDialog = dialog;
//
//                dialog.addWindowListener(new java.awt.event.WindowAdapter() {
//                    @Override
//                    public void windowClosing(java.awt.event.WindowEvent e) {
//                        isTableActive = false;
//                        currentDialog = null;
//                    }
//                });
//
//                // Add filter field
//                JPanel topPanel = new JPanel(new BorderLayout());
//                JTextField filterField = new JTextField();
//                filterField.setToolTipText("Type to filter by name");
//
//                // Add type filter dropdown
//                JPanel filterPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
//                JLabel nameFilterLabel = new JLabel("Name: ");
//                filterPanel.add(nameFilterLabel);
//                filterPanel.add(filterField);
//                filterPanel.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 5));
//
//                // Create type dropdown
//                JLabel typeFilterLabel = new JLabel("Type: ");
//                String[] typeOptions = {"All", "Power Provider", "Power Consumer"};
//                JComboBox<String> typeDropdown = new JComboBox<>(typeOptions);
//                filterPanel.add(typeFilterLabel);
//                filterPanel.add(typeDropdown);
//                typeDropdown.addActionListener(e -> {
//                    String selectedType = (String)typeDropdown.getSelectedItem();
//                    if (selectedType.equals("All")) {
//                        // If "All" is selected, only filter by name
//                        String text = filterField.getText();
//                        if (text.trim().length() == 0) {
//                            sorter.setRowFilter(null);
//                        } else {
//                            sorter.setRowFilter(RowFilter.regexFilter("(?i)" + text, 0));
//                        }
//                    } else {
//                        // Filter by both name and selected type
//                        String text = filterField.getText();
//                        if (text.trim().length() == 0) {
//                            sorter.setRowFilter(RowFilter.regexFilter("^" + selectedType + "$", 1));
//                        } else {
//                            // Combine name and type filters
//                            List<RowFilter<DefaultTableModel, Integer>> filters = new ArrayList<>();
//                            filters.add(RowFilter.regexFilter("(?i)" + text, 0));
//                            filters.add(RowFilter.regexFilter("^" + selectedType + "$", 1));
//                            sorter.setRowFilter(RowFilter.andFilter(filters));
//                        }
//                    }
//                });
//
//                filterField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
//                    @Override
//                    public void insertUpdate(javax.swing.event.DocumentEvent e) {
//                        updateFilter();
//                    }
//
//                    @Override
//                    public void removeUpdate(javax.swing.event.DocumentEvent e) {
//                        updateFilter();
//                    }
//
//                    @Override
//                    public void changedUpdate(javax.swing.event.DocumentEvent e) {
//                        updateFilter();
//                    }
//
//                    private void updateFilter() {
//                        String text = filterField.getText();
//                        String selectedType = (String)typeDropdown.getSelectedItem();
//
//                        if (selectedType.equals("All")) {
//                            if (text.trim().length() == 0) {
//                                sorter.setRowFilter(null);
//                            } else {
//                                sorter.setRowFilter(RowFilter.regexFilter("(?i)" + text, 0));
//                            }
//                        } else {
//                            List<RowFilter<DefaultTableModel, Integer>> filters = new ArrayList<>();
//                            if (text.trim().length() > 0) {
//                                filters.add(RowFilter.regexFilter("(?i)" + text, 0));
//                            }
//                            filters.add(RowFilter.regexFilter("^" + selectedType + "$", 1));
//                            sorter.setRowFilter(RowFilter.andFilter(filters));
//                        }
//                    }
//                });
//
//                topPanel.add(filterPanel, BorderLayout.CENTER);
//                filterField.setPreferredSize(new Dimension(200, 25));
//                dialog.getContentPane().setLayout(new BorderLayout());
//                dialog.getContentPane().add(topPanel, BorderLayout.NORTH);
//                dialog.getContentPane().add(scrollPane, BorderLayout.CENTER);
//
//                // Add export button
//                JButton exportButton = new JButton("Export to CSV");
//                exportButton.addActionListener(e -> exportTableToCSV(table));
//                filterPanel.add(exportButton);
//
//                // Assemble dialog
//                dialog.setSize(650, 350);
//                table.getColumnModel().getColumn(0).setPreferredWidth(150);  // Asset name column
//                table.getColumnModel().getColumn(1).setPreferredWidth(100);  // Type column
////                table.getColumnModel().getColumn(2).setPreferredWidth(80);   // Power column
//                table.setIntercellSpacing(new Dimension(5, 3));
//                dialog.setLocationRelativeTo(null);
//                dialog.setVisible(true);
//
//                Utilities.Log("Power table created successfully");
//
//            } catch (Exception e) {
//                Utilities.Log("Error creating power table: " + e.getMessage());
//                e.printStackTrace();
//            }
//        });
//    }
//
//    private static void populateTableWithModelData(DefaultTableModel model, Project project) {
//        Map<String, List<Element>> powerElements = Utilities.ModelElements.categorizePowerElements(project);
//        boolean hasElements = false;
//
//        // Add power providers
//        for (Element provider : powerElements.get("providers")) {
//            if (provider instanceof NamedElement) {
//                String name = ((NamedElement)provider).getName();
//                model.addRow(new Object[]{name, "Power Provider"});
//                hasElements = true;
//            }
//        }
//
//        // Add power consumers
//        for (Element consumer : powerElements.get("consumers")) {
//            if (consumer instanceof NamedElement) {
//                String name = ((NamedElement)consumer).getName();
//                model.addRow(new Object[]{name, "Power Consumer"});
//                hasElements = true;
//            }
//        }
//
//        // If no elements were found
//        if (!hasElements) {
//            JOptionPane.showMessageDialog(
//                    null,
//                    "No power elements found in the model. Please import system with power elements.",
//                    "No Power Elements",
//                    JOptionPane.INFORMATION_MESSAGE
//            );
//
//            // Add a single empty row with an informational message
//            model.addRow(new Object[]{"No power elements found", "", ""});
//        }
//    }
//
//    private static void exportTableToCSV(JTable table) {
//        try {
//            JFileChooser fileChooser = new JFileChooser();
//            fileChooser.setDialogTitle("Save CSV File");
//            fileChooser.setFileFilter(new javax.swing.filechooser.FileFilter() {
//                @Override
//                public boolean accept(java.io.File f) {
//                    return f.isDirectory() || f.getName().toLowerCase().endsWith(".csv");
//                }
//
//                @Override
//                public String getDescription() {
//                    return "CSV Files (*.csv)";
//                }
//            });
//
//            if (fileChooser.showSaveDialog(table) == JFileChooser.APPROVE_OPTION) {
//                java.io.File file = fileChooser.getSelectedFile();
//                if (!file.getName().toLowerCase().endsWith(".csv")) {
//                    file = new java.io.File(file.getAbsolutePath() + ".csv");
//                }
//
//                try (java.io.PrintWriter writer = new java.io.PrintWriter(file)) {
//                    // Write header
//                    for (int i = 0; i < table.getColumnCount(); i++) {
//                        writer.print(table.getColumnName(i));
//                        if (i < table.getColumnCount() - 1) writer.print(",");
//                    }
//                    writer.println();
//
//                    // Write data
//                    for (int row = 0; row < table.getRowCount(); row++) {
//                        for (int col = 0; col < table.getColumnCount(); col++) {
//                            Object value = table.getValueAt(row, col);
//                            writer.print(value != null ? value.toString() : "");
//                            if (col < table.getColumnCount() - 1) writer.print(",");
//                        }
//                        writer.println();
//                    }
//                }
//
//                Utilities.Log("Table exported to: " + file.getAbsolutePath());
//                JOptionPane.showMessageDialog(table, "Table exported successfully to:\n" +
//                        file.getAbsolutePath(), "Export Successful", JOptionPane.INFORMATION_MESSAGE);
//            }
//        } catch (Exception e) {
//            Utilities.Log("Error exporting table: " + e.getMessage());
//            JOptionPane.showMessageDialog(table, "Error exporting table: " + e.getMessage(),
//                    "Export Error", JOptionPane.ERROR_MESSAGE);
//        }
//    }
//}