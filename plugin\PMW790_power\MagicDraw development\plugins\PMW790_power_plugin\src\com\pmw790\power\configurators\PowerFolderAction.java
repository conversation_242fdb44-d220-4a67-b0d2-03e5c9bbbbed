package com.pmw790.power.configurators;

import com.nomagic.magicdraw.openapi.uml.SessionManager;
import com.nomagic.magicdraw.ui.browser.actions.DefaultBrowserAction;
import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.pmw790.power.diagram.PowerDiagramManager;
import com.pmw790.power.functions.SysMLStereotypes;
import com.pmw790.power.functions.Utilities;

import java.awt.event.ActionEvent;

import static com.pmw790.power.functions.Utilities.Log;

public class PowerFolderAction extends DefaultBrowserAction {
	private Element targetElement;

	public PowerFolderAction(Element element) {
		super("CREATE_POWER_PACKAGE", "PAR Diagram", null, null);
		this.targetElement = element;
	}

	@Override
	public void actionPerformed(ActionEvent e) {
		// Ensure SysMLStereotypes are initialized before proceeding
		if (!SysMLStereotypes.ensureInitialized()) {
			Log("Error: SysMLStereotypes initialization failed. Cannot create power diagram.");
			return;
		}

		Project project = SysMLStereotypes.getProject();
		if (project == null) return;

		if (!Utilities.ModelElements.isBlock(targetElement)) {
			Log("Not a valid block! Please select a Cabinet or Room block.");
			return;
		}

		try {
			SessionManager.getInstance().createSession("Create Power Diagram");

			PowerDiagramManager.createParametricDiagram(project, targetElement);

			SessionManager.getInstance().closeSession();
		} catch (Exception ex) {
			SessionManager.getInstance().cancelSession();
			Log("Error creating power diagram: " + ex.getMessage());
		}
	}
}
