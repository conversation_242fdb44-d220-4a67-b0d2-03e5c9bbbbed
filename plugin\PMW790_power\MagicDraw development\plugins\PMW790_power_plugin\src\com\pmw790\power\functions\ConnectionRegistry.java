package com.pmw790.power.functions;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.jmi.helpers.StereotypesHelper;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Package;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Stereotype;
import com.pmw790.power.functions.SysMLStereotypes;
import com.pmw790.power.functions.Utilities;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdinternalstructures.ConnectorEnd;
import java.util.*;

import static com.pmw790.power.functions.Utilities.Log;

/**
 * Registry for power connections and relationships between model elements.
 * Maintains information about power providers, consumers, cabinets, rooms,
 * and their interconnections.
 */
public class ConnectionRegistry {
    private static ConnectionRegistry instance;
    private boolean isAnalyzed = false;
    private ModelAnalyzer modelAnalyzer;
    private RelationshipAnalyzer relationshipAnalyzer;

    //--------------------------------------------------------------------------
    // Element type and property data
    //--------------------------------------------------------------------------
    private Map<String, String> elementTypeMap = new HashMap<>();
    private Map<String, Map<String, Object>> elementPropsMap = new HashMap<>();
    private Map<String, Set<String>> adjacencyMap = new HashMap<>();

    //--------------------------------------------------------------------------
    // Power connection data
    //--------------------------------------------------------------------------
    private Map<String, Set<String>> powerSources = new HashMap<>();
    private Map<String, Set<String>> powerTargets = new HashMap<>();

    //--------------------------------------------------------------------------
    // Cabinet data
    //--------------------------------------------------------------------------
    private Set<String> cabinets = new HashSet<>();
    private Map<String, Class> cabinetBlocksMap = new HashMap<>();
    private Map<String, List<String>> cabinetToPowerProviders = new HashMap<>();
    private Map<String, Set<String>> cabinetTopProviders = new HashMap<>();
    private Map<String, Set<String>> cabinetProvidersWithRoomConnections = new HashMap<>();

    //--------------------------------------------------------------------------
    // Room data
    //--------------------------------------------------------------------------
    private Set<String> rooms = new HashSet<>();
    private Map<String, List<String>> roomToCabinets = new HashMap<>();
    private Map<String, Map<String, List<String>>> roomToPowerProviders = new HashMap<>();
    private Set<String> roomLevelPowerProviders = new HashSet<>();

    private ConnectionRegistry() {
        // Initialize inner class instances
        modelAnalyzer = new ModelAnalyzer();
        relationshipAnalyzer = new RelationshipAnalyzer();
    }

    /**
     * Gets the singleton instance of ConnectionRegistry
     * @return The ConnectionRegistry instance
     */
    public static synchronized ConnectionRegistry getInstance() {
        if (instance == null) {
            instance = new ConnectionRegistry();
        }
        return instance;
    }

    //--------------------------------------------------------------------------
    // Data access methods
    //--------------------------------------------------------------------------

    /**
     * Checks if the model has been analyzed
     * @return true if the model has been analyzed, false otherwise
     */
    public boolean isModelAnalyzed() {
        return isAnalyzed;
    }

    /**
     * Gets the type of an element by name
     * @param elementName The name of the element
     * @return The element type or "UNKNOWN" if not found
     */
    public String getElementType(String elementName) {
        return elementTypeMap.getOrDefault(elementName, "UNKNOWN");
    }

    /**
     * Gets all power-related connections in the model
     * @return Map of element names to their connected elements
     */
    public Map<String, Set<String>> getPowerRelatedConnections() {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return new HashMap<>();
        }
        return adjacencyMap;
    }

    /**
     * Gets all power sources in the model
     * @return Map of element names to their power sources
     */
    public Map<String, Set<String>> getPowerSources() {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return new HashMap<>();
        }
        return powerSources;
    }

    /**
     * Gets power sources for a specific element
     * @param elementName The name of the element
     * @return Set of power source names for the element
     */
    public Set<String> getPowerSources(String elementName) {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return Collections.emptySet();
        }
        return powerSources.getOrDefault(elementName, Collections.emptySet());
    }

    /**
     * Gets all power targets in the model
     * @return Map of element names to their power targets
     */
    public Map<String, Set<String>> getPowerTargets() {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return new HashMap<>();
        }
        return powerTargets;
    }

    /**
     * Gets power targets for a specific element
     * @param elementName The name of the element
     * @return Set of power target names for the element
     */
    public Set<String> getPowerTargets(String elementName) {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return Collections.emptySet();
        }
        return powerTargets.getOrDefault(elementName, Collections.emptySet());
    }

    /**
     * Gets all element properties in the model
     * @return Map of element names to their properties
     */
    public Map<String, Map<String, Object>> getElementProperties() {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return new HashMap<>();
        }
        return elementPropsMap;
    }

    /**
     * Gets all power providers in the model
     * @return Set of power provider names
     */
    public Set<String> getPowerProviders() {
        Set<String> providers = new HashSet<>();
        for (String element : elementTypeMap.keySet()) {
            if ("POWER_PROVIDER".equals(elementTypeMap.get(element))) {
                providers.add(element);
            }
        }
        return providers;
    }

    /**
     * Gets all power consumers in the model
     * @return Set of power consumer names
     */
    public Set<String> getPowerConsumers() {
        Set<String> consumers = new HashSet<>();
        for (String element : elementTypeMap.keySet()) {
            if ("POWER_CONSUMER".equals(elementTypeMap.get(element))) {
                consumers.add(element);
            }
        }
        return consumers;
    }

    //--------------------------------------------------------------------------
    // Cabinet data access methods
    //--------------------------------------------------------------------------

    /**
     * Gets all cabinets in the model
     * @return Set of cabinet names
     */
    public Set<String> getCabinets() {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return new HashSet<>();
        }
        return cabinets;
    }

    /**
     * Gets the cabinet block by name
     * @param cabinetName The name of the cabinet
     * @return The cabinet block or null if not found
     */
    public Class getCabinetBlockByName(String cabinetName) {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return null;
        }
        return cabinetBlocksMap.get(cabinetName);
    }

    /**
     * Gets the mapping of cabinets to their power providers
     * @return Map of cabinet names to their power provider names
     */
    public Map<String, List<String>> getCabinetToPowerProviders() {
        if (!isAnalyzed) {
            return new HashMap<>();
        }
        return cabinetToPowerProviders;
    }

    /**
     * Gets the mapping of cabinets to their top-level power providers
     * Top providers are those that either have room connections or no internal cabinet sources
     * @return Map of cabinet names to their top power provider names
     */
    public Map<String, Set<String>> getCabinetTopProviders() {
        if (!isAnalyzed) {
            return new HashMap<>();
        }
        return cabinetTopProviders;
    }

    //--------------------------------------------------------------------------
    // Room data access methods
    //--------------------------------------------------------------------------

    /**
     * Gets all rooms in the model
     * @return Set of room names
     */
    public Set<String> getRooms() {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return new HashSet<>();
        }
        return rooms;
    }

    /**
     * Gets cabinets for a specific room
     * @param roomName The name of the room
     * @return List of cabinet names in the room
     */
    public List<String> getCabinetsForRoom(String roomName) {
        if (!isAnalyzed) {
            Log("Model connections have not been analyzed yet");
            return new ArrayList<>();
        }
        return roomToCabinets.getOrDefault(roomName, new ArrayList<>());
    }

    /**
     * Gets the mapping of rooms to their power providers
     * @return Map of room names to their power provider hierarchy (Parent Provider -> List of Child Providers)
     */
    public Map<String, Map<String, List<String>>> getRoomToPowerProviders() {
        if (!isAnalyzed) {
            return new HashMap<>();
        }
        return roomToPowerProviders;
    }

    /**
     * Gets power consumers for a specific provider using pre-computed power targets map
     * @param providerName The provider name
     * @return List of consumer names for the provider
     */
    public List<String> getConsumersForProvider(String providerName) {
        if (!isAnalyzed || providerName == null) {
            return Collections.emptyList();
        }

        Set<String> targets = powerTargets.getOrDefault(providerName, Collections.emptySet());
        List<String> consumers = new ArrayList<>();

        for (String target : targets) {
            String targetType = elementTypeMap.get(target);
            if (Utilities.TYPE_POWER_CONSUMER.equals(targetType)) {
                consumers.add(target);
            }
        }

        return consumers;
    }

    /**
     * Checks if a cabinet has providers with consumers using optimized lookups
     * @param cabinetName The cabinet name
     * @return true if the cabinet has providers that have consumers
     */
    public boolean hasProvidersWithConsumers(String cabinetName) {
        if (!isAnalyzed || cabinetName == null) {
            return false;
        }

        List<String> providers = cabinetToPowerProviders.getOrDefault(cabinetName, Collections.emptyList());
        for (String provider : providers) {
            if (!getConsumersForProvider(provider).isEmpty()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Gets all cabinets that have providers with consumers
     * @return Set of cabinet names that have active providers
     */
    public Set<String> getCabinetsWithActiveProviders() {
        if (!isAnalyzed) {
            return Collections.emptySet();
        }

        Set<String> activeCabinets = new HashSet<>();
        for (String cabinetName : cabinets) {
            if (hasProvidersWithConsumers(cabinetName)) {
                activeCabinets.add(cabinetName);
            }
        }

        return activeCabinets;
    }

    //--------------------------------------------------------------------------
    // Inner classes for analysis functionality
    //--------------------------------------------------------------------------

    /**
     * Inner class for model analysis functionality
     */
    public class ModelAnalyzer {
        /**
         * Analyzes all model connections and builds the power network graph
         * @param project The MagicDraw project
         */
        public synchronized void analyzeModelConnections(Project project) {
            try {
                if (!SysMLStereotypes.ensureInitialized()) {
                    Log("Error: SysMLStereotypes initialization failed. Cannot analyze model connections.");
                    return;
                }
                reset();

                elementTypeMap = Utilities.ModelElements.categorizePowerElements(project, elementPropsMap, cabinets, cabinetBlocksMap);
                collectRoomToCabinets(project);
                categorizeProvidersByLevelAndCabinet();
                collectRoomToPowerProviders();

                Stereotype nestedConnectorEndStereotype = SysMLStereotypes.getNestedConnectorEndStereotype();
                Package locationViewPackage = SysMLStereotypes.getLocationViewPackage(project);
                if (locationViewPackage != null) {
                    processLocationViewConnections(locationViewPackage, nestedConnectorEndStereotype);
                }
                Package assetsViewPackage = SysMLStereotypes.getAssetsViewPackage(project);
                if (assetsViewPackage != null) {
                    processAssetsViewConnections(assetsViewPackage, nestedConnectorEndStereotype);
                }

                isAnalyzed = true;
            } catch (Exception e) {
                Log("Error analyzing model connections: " + e.getMessage());
                isAnalyzed = false;
            }
        }

        /**
         * Processes connections in the Location View package
         * Only processes connectors owned by Room blocks
         * @param locationPackage The Location View package
         * @param nestedConnectorEndStereotype The nested connector end stereotype
         */
        private void processLocationViewConnections(Package locationPackage, Stereotype nestedConnectorEndStereotype) {
            List<Element> connectors = Utilities.getAllRoomConnectors();

            Map<String, Set<String>> tempAdjacencyMap = new HashMap<>();

            for (Element connector : connectors) {
                List<String> connectedElements = getConnectedElements(connector, nestedConnectorEndStereotype);
                if (connectedElements.size() == 2) {
                    String element1 = connectedElements.get(0);
                    String element2 = connectedElements.get(1);

                    tempAdjacencyMap.computeIfAbsent(element1, k -> new HashSet<>()).add(element2);
                    tempAdjacencyMap.computeIfAbsent(element2, k -> new HashSet<>()).add(element1);
                }
            }

            // Now resolve power connections through connector chains
            Set<String> processedElements = new HashSet<>();

            for (String element : tempAdjacencyMap.keySet()) {
                String elementType = elementTypeMap.get(element);
                if (("POWER_PROVIDER".equals(elementType) || "POWER_CONSUMER".equals(elementType)) && !processedElements.contains(element)) {
                    // Find all connected power elements through connector chains
                    Set<String> connectedElements = findConnectedPowerElements(element, tempAdjacencyMap, new HashSet<>());

                    // Separate providers and consumers
                    Set<String> providers = new HashSet<>();
                    Set<String> consumers = new HashSet<>();

                    for (String connected : connectedElements) {
                        String connectedType = elementTypeMap.get(connected);
                        if ("POWER_PROVIDER".equals(connectedType)) {
                            providers.add(connected);
                        } else if ("POWER_CONSUMER".equals(connectedType)) {
                            consumers.add(connected);
                        }
                    }

                    // Apply hierarchy rules for provider-provider connections
                    List<String> providerList = new ArrayList<>(providers);
                    for (int i = 0; i < providerList.size(); i++) {
                        for (int j = i + 1; j < providerList.size(); j++) {
                            applyHierarchyRules(providerList.get(i), providerList.get(j));
                        }
                    }

                    // Handle provider-consumer connections (room-level providers to consumers in cabinets)
                    for (String provider : providers) {
                        for (String consumer : consumers) {
                            applyProviderConsumerRules(provider, consumer);
                        }
                    }

                    processedElements.addAll(connectedElements);
                }
            }
        }

        /**
         * Recursively finds all power providers and consumers connected through connector chains
         * @param startElement The starting element name
         * @param tempAdjacencyMap The temporary adjacency map
         * @param visited Set of visited element names
         * @return Set of connected power element names
         */
        private Set<String> findConnectedPowerElements(String startElement, Map<String, Set<String>> tempAdjacencyMap, Set<String> visited) {
            Set<String> connectedElements = new HashSet<>();

            if (visited.contains(startElement)) {
                return connectedElements;
            }

            visited.add(startElement);

            // If this is a power element, add it to results
            String elementType = elementTypeMap.get(startElement);
            if ("POWER_PROVIDER".equals(elementType) || "POWER_CONSUMER".equals(elementType)) {
                connectedElements.add(startElement);
            }

            // Traverse through all neighbors
            Set<String> neighbors = tempAdjacencyMap.getOrDefault(startElement, new HashSet<>());
            for (String neighbor : neighbors) {
                if (!visited.contains(neighbor)) {
                    String normalizedNeighbor = normalizeElementName(neighbor);
                    String neighborType = elementTypeMap.get(normalizedNeighbor);

                    if ("POWER_PROVIDER".equals(neighborType) || "POWER_CONSUMER".equals(neighborType)) {
                        connectedElements.add(neighbor);
                    } else if ("CONNECTOR".equals(neighborType)) {
                        connectedElements.addAll(findConnectedPowerElements(neighbor, tempAdjacencyMap, visited));
                    }
                }
            }

            return connectedElements;
        }

        /**
         * Applies rules for provider-consumer connections in Location View
         * @param provider The provider element name
         * @param consumer The consumer element name
         */
        private void applyProviderConsumerRules(String provider, String consumer) {
            boolean providerIsRoomLevel = isRoomLevelProvider(provider);
            String consumerCabinet = getCabinetForConsumer(consumer);

            // Room-level provider connects to consumer in cabinet
            if (providerIsRoomLevel && consumerCabinet != null) {
                addDirectedPowerConnection(provider, consumer);
            }
        }

        /**
         * Applies rules for provider-provider connections within cabinets (Assets View)
         * @param provider1 The first provider element name
         * @param provider2 The second provider element name
         */
        private void applyCabinetProviderRules(String provider1, String provider2) {
            String cabinet1 = getCabinetForProvider(provider1);
            String cabinet2 = getCabinetForProvider(provider2);

            // Only process if both providers are in the same cabinet
            if (cabinet1 != null && cabinet1.equals(cabinet2)) {
                String cabinetName = cabinet1;
                boolean provider1HasRoomConnection =
                        cabinetProvidersWithRoomConnections.containsKey(cabinetName) &&
                                cabinetProvidersWithRoomConnections.get(cabinetName).contains(provider1);
                boolean provider2HasRoomConnection =
                        cabinetProvidersWithRoomConnections.containsKey(cabinetName) &&
                                cabinetProvidersWithRoomConnections.get(cabinetName).contains(provider2);

                // Provider with room connection feeds provider without room connection
                if (provider1HasRoomConnection && !provider2HasRoomConnection) {
                    addDirectedPowerConnection(provider1, provider2);
                }
                else if (!provider1HasRoomConnection && provider2HasRoomConnection) {
                    addDirectedPowerConnection(provider2, provider1);
                }
                // Both have same connection status - bidirectional
                else {
                    adjacencyMap.computeIfAbsent(provider1, k -> new HashSet<>()).add(provider2);
                    adjacencyMap.computeIfAbsent(provider2, k -> new HashSet<>()).add(provider1);
                }
            }
        }

        /**
         * Applies room/cabinet hierarchy rules to a pair of connected power providers
         * @param provider1 The first provider element name
         * @param provider2 The second provider element name
         */
        private void applyHierarchyRules(String provider1, String provider2) {
            boolean provider1IsRoomLevel = isRoomLevelProvider(provider1);
            boolean provider2IsRoomLevel = isRoomLevelProvider(provider2);

            String cabinet1 = getCabinetForProvider(provider1);
            String cabinet2 = getCabinetForProvider(provider2);

            // Room-level provider connects to cabinet-level provider
            if (provider1IsRoomLevel && cabinet2 != null) {
                addDirectedPowerConnection(provider1, provider2);
                cabinetProvidersWithRoomConnections
                        .computeIfAbsent(cabinet2, k -> new HashSet<>())
                        .add(provider2);
            }
            else if (provider2IsRoomLevel && cabinet1 != null) {
                addDirectedPowerConnection(provider2, provider1);
                cabinetProvidersWithRoomConnections
                        .computeIfAbsent(cabinet1, k -> new HashSet<>())
                        .add(provider1);
            }
            // Both are room-level (bidirectional)
            else if (provider1IsRoomLevel && provider2IsRoomLevel) {
                adjacencyMap.computeIfAbsent(provider1, k -> new HashSet<>()).add(provider2);
                adjacencyMap.computeIfAbsent(provider2, k -> new HashSet<>()).add(provider1);
            }
        }

        /**
         * Processes connections in the Assets View package
         * @param assetsPackage The Assets View package
         * @param nestedConnectorEndStereotype The nested connector end stereotype
         */
        private void processAssetsViewConnections(Package assetsPackage, Stereotype nestedConnectorEndStereotype) {
            List<Element> connectors = new ArrayList<>();
            Utilities.ModelElements.findConnectors(assetsPackage, connectors);

            // Build temporary adjacency map with ALL connections (including connectors)
            Map<String, Set<String>> tempAdjacencyMap = new HashMap<>();

            for (Element connector : connectors) {
                List<String> connectedElements = getConnectedElements(connector, nestedConnectorEndStereotype);
                if (connectedElements.size() == 2) {
                    String element1 = connectedElements.get(0);
                    String element2 = connectedElements.get(1);

                    if (!elementTypeMap.containsKey(element1) || !elementTypeMap.containsKey(element2)) {
                        continue;
                    }

                    // Build bidirectional adjacency (including connectors)
                    tempAdjacencyMap.computeIfAbsent(element1, k -> new HashSet<>()).add(element2);
                    tempAdjacencyMap.computeIfAbsent(element2, k -> new HashSet<>()).add(element1);
                }
            }

            // Resolve power connections through connector chains
            Set<String> processedElements = new HashSet<>();

            for (String element : tempAdjacencyMap.keySet()) {
                String elementType = elementTypeMap.get(element);
                if (("POWER_PROVIDER".equals(elementType) || "POWER_CONSUMER".equals(elementType)) && !processedElements.contains(element)) {
                    Set<String> connectedElements = findConnectedPowerElements(element, tempAdjacencyMap, new HashSet<>());

                    // Separate providers and consumers
                    Set<String> providers = new HashSet<>();
                    Set<String> consumers = new HashSet<>();

                    for (String connected : connectedElements) {
                        String connectedType = elementTypeMap.get(connected);
                        if ("POWER_PROVIDER".equals(connectedType)) {
                            providers.add(connected);
                        } else if ("POWER_CONSUMER".equals(connectedType)) {
                            consumers.add(connected);
                        }
                    }

                    // Handle provider-consumer connections
                    for (String provider : providers) {
                        for (String consumer : consumers) {
                            addDirectedPowerConnection(provider, consumer);
                        }
                    }

                    // Handle provider-provider connections within same cabinet
                    List<String> providerList = new ArrayList<>(providers);
                    for (int i = 0; i < providerList.size(); i++) {
                        for (int j = i + 1; j < providerList.size(); j++) {
                            applyCabinetProviderRules(providerList.get(i), providerList.get(j));
                        }
                    }

                    processedElements.addAll(connectedElements);
                }
            }

            cabinetTopProviders.clear();
            for (String cabinetName : cabinets) {
                List<String> providersInCabinet = cabinetToPowerProviders.getOrDefault(cabinetName, Collections.emptyList());
                Set<String> topProvidersInCabinet = new HashSet<>();

                if (cabinetProvidersWithRoomConnections.containsKey(cabinetName)) {
                    topProvidersInCabinet.addAll(cabinetProvidersWithRoomConnections.get(cabinetName));
                }

                for (String provider : providersInCabinet) {
                    Set<String> sourcesForProvider = powerSources.getOrDefault(provider, Collections.emptySet());
                    boolean hasInternalSourceInCabinet = false;
                    for (String source : sourcesForProvider) {
                        if (providersInCabinet.contains(source)) {
                            hasInternalSourceInCabinet = true;
                            break;
                        }
                    }
                    if (!hasInternalSourceInCabinet) {
                        topProvidersInCabinet.add(provider);
                    }
                }
                cabinetTopProviders.put(cabinetName, topProvidersInCabinet);
            }
        }
    }

    /**
     * Inner class for relationship analysis functionality
     */
    public class RelationshipAnalyzer {
        /**
         * Analyzes power relationships for a selected element
         * @param selectedElement The selected element
         * @return Map of element names to their power relationships
         */
        public synchronized Map<String, Map<String, List<String>>> analyzePowerRelationshipsForElement(Element selectedElement) {
            if (!(selectedElement instanceof NamedElement)) {
                return new HashMap<>();
            }

            String elementName = ((NamedElement) selectedElement).getName();

            // Result will have categories: "consumers" and "providers"
            Map<String, Map<String, List<String>>> result = new HashMap<>();
            result.put(elementName, new HashMap<>());
            result.get(elementName).put("consumers", new ArrayList<>());
            result.get(elementName).put("providers", new ArrayList<>());

            // Find direct connections
            Set<String> visited = new HashSet<>();
            Set<String> consumerEndPoints = new HashSet<>();
            Set<String> connectedProviders = new HashSet<>();

            // Use tracePath to collect providers and consumers
            tracePath(elementName, adjacencyMap, getPowerProviders(),
                    getPowerConsumers(), visited, consumerEndPoints, connectedProviders);

            // Add results to the structured map
            if (!consumerEndPoints.isEmpty()) {
                List<String> sortedEndPoints = new ArrayList<>(consumerEndPoints);
                Collections.sort(sortedEndPoints);
                result.get(elementName).put("consumers", sortedEndPoints);
            }

            if (!connectedProviders.isEmpty()) {
                List<String> sortedProviders = new ArrayList<>(connectedProviders);
                Collections.sort(sortedProviders);
                result.get(elementName).put("providers", sortedProviders);
            }

            return result;
        }

        /**
         * Traces paths through the power network to find connected providers and consumers
         * @param start The starting element name
         * @param adjacency The adjacency map
         * @param powerItems Set of power provider names
         * @param consumerItems Set of power consumer names
         * @param visited Set of visited element names
         * @param endPoints Set to collect consumer endpoints
         * @param powerConnections Set to collect connected power providers
         */
        private void tracePath(
                String start,
                Map<String, Set<String>> adjacency,
                Set<String> powerItems,
                Set<String> consumerItems,
                Set<String> visited,
                Set<String> endPoints,
                Set<String> powerConnections) {

            visited.add(start);

            Set<String> neighbors = adjacency.getOrDefault(start, new HashSet<>());

            for (String neighbor : neighbors) {
                if (visited.contains(neighbor)) {
                    continue;
                }

                if (powerItems.contains(neighbor)) {
                    // If neighbor is another power provider, add to connections but don't traverse
                    powerConnections.add(neighbor);
                } else if (consumerItems.contains(neighbor)) {
                    // This is a power consumer
                    endPoints.add(neighbor);
                } else {
                    // This is an intermediate element (like a cable)
                    tracePath(neighbor, adjacency, powerItems, consumerItems,
                            visited, endPoints, powerConnections);
                }
            }
        }
    }

    //--------------------------------------------------------------------------
    // Public methods to access inner class functionality
    //--------------------------------------------------------------------------

    /**
     * Analyzes all model connections and builds the power network graph
     * @param project The MagicDraw project
     */
    public synchronized void analyzeModelConnections(Project project) {
        modelAnalyzer.analyzeModelConnections(project);
    }

    /**
     * Analyzes power relationships for a selected element
     * @param selectedElement The selected element
     * @return Map of element names to their power relationships
     */
    public synchronized Map<String, Map<String, List<String>>> analyzePowerRelationshipsForElement(Element selectedElement) {
        return relationshipAnalyzer.analyzePowerRelationshipsForElement(selectedElement);
    }

    //--------------------------------------------------------------------------
    // Utility methods
    //--------------------------------------------------------------------------

    /**
     * Gets the cabinet that hosts a power consumer
     * @param consumerName The consumer element name
     * @return The cabinet name or null if not found
     */
    private String getCabinetForConsumer(String consumerName) {
        Map<String, Object> props = elementPropsMap.get(consumerName);
        if (props != null && props.containsKey("host_asset")) {
            Object hostAssetValue = props.get("host_asset");
            if (hostAssetValue instanceof String) {
                String hostAsset = (String) hostAssetValue;
                if (cabinets.contains(hostAsset)) {
                    return hostAsset;
                }
            }
        }
        return null;
    }

    /**
     * Tracks a parent-child power provider relationship in a room
     * @param roomName The room name
     * @param parentProvider The parent provider name
     * @param childProvider The child provider name
     */
    private void trackRoomProviderHierarchy(String roomName, String parentProvider, String childProvider) {
        if (roomName == null || parentProvider == null || childProvider == null) {
            return;
        }

        // Get or create room hierarchy
        Map<String, List<String>> roomHierarchy = roomToPowerProviders.get(roomName);
        if (roomHierarchy == null) {
            roomHierarchy = new HashMap<>();
            roomToPowerProviders.put(roomName, roomHierarchy);
        }

        // Get or create parent's children list
        List<String> childProviders = roomHierarchy.computeIfAbsent(
            parentProvider, k -> new ArrayList<>());

        // Add child if not already present
        if (!childProviders.contains(childProvider)) {
            childProviders.add(childProvider);
        }
    }

    /**
     * Finds the room that contains a power provider
     * @param providerName The provider name
     * @return The room name or null if not found
     */
    private String findRoomForProvider(String providerName) {
        Map<String, Object> props = elementPropsMap.get(providerName);
        if (props != null && props.containsKey("host_location")) {
            Object hostLocationObj = props.get("host_location");
            if (hostLocationObj instanceof String) {
                String hostLocation = (String) hostLocationObj;
                if (rooms.contains(hostLocation)) {
                    return hostLocation;
                }
            }
        }
        return null;
    }

    /**
     * Categorizes power providers as either room-level or cabinet-level based on their host_asset.
     * Populates roomLevelPowerProviders and cabinetToPowerProviders.
     */
    private void categorizeProvidersByLevelAndCabinet() {
        roomLevelPowerProviders.clear();
        cabinetToPowerProviders.clear();

        for (String cabinetName : cabinets) {
            cabinetToPowerProviders.put(cabinetName, new ArrayList<>());
        }

        for (Map.Entry<String, String> entry : elementTypeMap.entrySet()) {
            if ("POWER_PROVIDER".equals(entry.getValue())) {
                String providerName = entry.getKey();
                Map<String, Object> props = elementPropsMap.get(providerName);
                Object hostAssetObj = (props == null) ? null : props.get("host_asset");

                boolean isHostedByCabinet = false;
                String hostingCabinetName = null;

                if (hostAssetObj != null) {
                    if (hostAssetObj instanceof String) {
                        String hostAssetNameStr = (String) hostAssetObj;
                        if (cabinets.contains(hostAssetNameStr)) {
                            isHostedByCabinet = true;
                            hostingCabinetName = hostAssetNameStr;
                        }
                    } else {
                        Log("Warning: host_asset for provider '" + providerName +
                                "' is of type " + hostAssetObj.getClass().getName() +
                                " (expected String). Provider will be considered room-level.");
                    }
                }

                if (isHostedByCabinet) {
                    if (hostingCabinetName != null) {
                        cabinetToPowerProviders.get(hostingCabinetName).add(providerName);
                    }
                } else {
                    roomLevelPowerProviders.add(providerName);
                }
            }
        }
    }

    /**
     * Collects room to power provider relationships based on host_location property
     */
    private void collectRoomToPowerProviders() {
        roomToPowerProviders.clear();
        for (String roomName : rooms) {
            roomToPowerProviders.put(roomName, new HashMap<>());
        }
    }

    /**
     * Checks if a provider is a room-level provider
     * @param providerName The provider element name
     * @return true if the provider is room-level, false otherwise
     */
    private boolean isRoomLevelProvider(String providerName) {
        return roomLevelPowerProviders.contains(providerName);
    }

    /**
     * Gets the cabinet that hosts a power provider
     * @param providerName The provider element name
     * @return The cabinet name or null if not found
     */
    private String getCabinetForProvider(String providerName) {
        Map<String, Object> props = elementPropsMap.get(providerName);
        if (props != null && props.containsKey("host_asset")) {
            Object hostAssetValue = props.get("host_asset");
            if (hostAssetValue instanceof String) {
                String hostAsset = (String) hostAssetValue;
                if (cabinets.contains(hostAsset)) {
                    return hostAsset;
                }
            }
        }
        return null;
    }

    /**
     * Adds a directed power connection between two elements
     * @param source The source element name
     * @param target The target element name
     */
    private void  addDirectedPowerConnection(String source, String target) {
        powerTargets.computeIfAbsent(source, k -> new HashSet<>()).add(target);
        powerSources.computeIfAbsent(target, k -> new HashSet<>()).add(source);
        adjacencyMap.computeIfAbsent(source, k -> new HashSet<>()).add(target);
        adjacencyMap.computeIfAbsent(target, k -> new HashSet<>()).add(source);
    }

    /**
     * Gets elements connected by a connector
     * @param connector The connector element
     * @param nestedConnectorEndStereotype The nested connector end stereotype
     * @return List of connected element names
     */
    private List<String> getConnectedElements(Element connector, Stereotype nestedConnectorEndStereotype) {
        List<String> connectedElements = new ArrayList<>();

        for (Element owned : connector.getOwnedElement()) {
            if (owned instanceof ConnectorEnd) {
                ConnectorEnd connectorEnd = (ConnectorEnd) owned;
                Element role = connectorEnd.getRole();

                if (role instanceof NamedElement) {
                    String elementName;

                    if (StereotypesHelper.hasStereotype(connectorEnd, nestedConnectorEndStereotype)) {
                        List<?> partPathValues = StereotypesHelper.getStereotypePropertyValue(
                                connectorEnd,
                                nestedConnectorEndStereotype,
                                "propertyPath");

                        if (partPathValues != null && !partPathValues.isEmpty()) {
                            Object lastElementInPath = partPathValues.get(partPathValues.size() - 1);

                            // Track parent-child hierarchy if path has multiple elements
                            if (partPathValues.size() > 1) {
                                Object firstElementInPath = partPathValues.get(0);

                                if (firstElementInPath instanceof NamedElement && lastElementInPath instanceof NamedElement) {
                                    String parentName = ((NamedElement) firstElementInPath).getName();
                                    String childName = ((NamedElement) lastElementInPath).getName();

                                    // Check if both parent and child are power providers (not room-to-provider)
                                    String parentType = elementTypeMap.get(parentName);
                                    String childType = elementTypeMap.get(childName);

                                    if (Utilities.TYPE_POWER_PROVIDER.equals(parentType)) {
                                        // Find room for parent provider and track hierarchy
                                        String roomName = findRoomForProvider(parentName);
                                        if (roomName != null) {
                                            trackRoomProviderHierarchy(roomName, parentName, childName);
                                        }
                                    }
                                }
                            }

                            if (lastElementInPath instanceof NamedElement) {
                                elementName = ((NamedElement) lastElementInPath).getName();
                            } else {
                                elementName = ((NamedElement) role).getName();
                            }
                        } else {
                            elementName = ((NamedElement) role).getName();
                        }
                    } else {
                        elementName = ((NamedElement) role).getName();
                    }

                    connectedElements.add(elementName);
                }
            }
        }
        return connectedElements;
    }

    private String normalizeElementName(String elementName) {
        if (elementName == null) return null;

        // Only normalize if pattern contains underscore followed by dash
        int underscoreIndex = elementName.lastIndexOf('_');
        if (underscoreIndex > 0) {
            int dashAfterUnderscore = elementName.indexOf('-', underscoreIndex);
            if (dashAfterUnderscore > underscoreIndex) {
                return elementName.substring(0, dashAfterUnderscore);
            }
        }
        return elementName;
    }

    /**
     * Collects room to cabinet relationships based on host_location property
     * @param project The MagicDraw project
     */
    private void collectRoomToCabinets(Project project) {
        rooms.clear();
        roomToCabinets.clear();

        Package locationPackage = SysMLStereotypes.getLocationViewPackage(project);
        if (locationPackage == null) {
            Log("Warning: Location View package not found. Room and cabinet location data may be incomplete.");
            return;
        }

        // Use cached room names for improved performance
        Set<String> roomNames = Utilities.getRoomNamesFromCache(project);
        rooms.addAll(roomNames);
        for (String roomName : roomNames) {
            roomToCabinets.put(roomName, new ArrayList<>());
        }

        for (String cabinetName : cabinets) {
            String hostLocation = Utilities.CabinetProperties.getCabinetHostLocationFromCache(cabinetName);

            if (hostLocation != null && rooms.contains(hostLocation)) {
                roomToCabinets.computeIfAbsent(hostLocation, k -> new ArrayList<>()).add(cabinetName);
            }
        }
    }

    /**
     * Resets all data structures in the registry
     */
    public void reset() {
        adjacencyMap.clear();
        elementTypeMap.clear();
        elementPropsMap.clear();
        cabinets.clear();
        cabinetBlocksMap.clear();

        roomLevelPowerProviders.clear();
        cabinetToPowerProviders.clear();

        rooms.clear();
        roomToCabinets.clear();
        roomToPowerProviders.clear();

        powerSources.clear();
        powerTargets.clear();
        cabinetTopProviders.clear();
        cabinetProvidersWithRoomConnections.clear();

        isAnalyzed = false;
    }
}