package com.pmw790.power.functions;

import com.nomagic.magicdraw.core.Application;
import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.jmi.helpers.StereotypesHelper;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Package;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Stereotype;

import javax.swing.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class Utilities {

    /**
     * Class to hold classifier information including the classifier name and the Product Catalog element.
     */
    public static class ClassifierInfo {
        private String classifierName;
        private Element productCatalogElement;

        public ClassifierInfo(String classifierName, Element productCatalogElement) {
            this.classifierName = classifierName;
            this.productCatalogElement = productCatalogElement;
        }

        public String getClassifierName() {
            return classifierName;
        }

        public Element getProductCatalogElement() {
            return productCatalogElement;
        }
    }

    /**
     * Class to hold property collection results including both property values and Property objects.
     */
    public static class PropertyCollection {
        private Map<String, Object> propertyValues;
        private Map<String, Property> propertyObjects;

        public PropertyCollection() {
            this.propertyValues = new HashMap<>();
            this.propertyObjects = new HashMap<>();
        }

        public PropertyCollection(Map<String, Object> propertyValues, Map<String, Property> propertyObjects) {
            this.propertyValues = propertyValues;
            this.propertyObjects = propertyObjects;
        }

        public Map<String, Object> getPropertyValues() {
            return propertyValues;
        }

        public Map<String, Property> getPropertyObjects() {
            return propertyObjects;
        }

        public void addProperty(String name, Object value, Property property) {
            if (value != null) {
                propertyValues.put(name, value);
            }
            if (property != null) {
                propertyObjects.put(name, property);
            }
        }
    }

    //--------------------------------------------------------------------------
    // 1. CONSTANTS
    //--------------------------------------------------------------------------
    public static final String STEREOTYPE_BLOCK = "Block";
    public static final String PACKAGE_PRODUCT_CATALOG = "Product Catalog";
    public static final String PACKAGE_IDP_TAXONOMY = "IDP Taxonomy";
    public static final String PROP_HOST_ASSET = "host_asset";
    public static final String PROP_HOST_LOCATION = "host_location";
    public static final String TYPE_POWER_PROVIDER = "POWER_PROVIDER";
    public static final String TYPE_POWER_CONSUMER = "POWER_CONSUMER";
    public static final String TYPE_CONNECTOR = "CONNECTOR";
	public static final String TYPE_CABINET = "CABINET";
    public static final String TYPE_ROOM = "ROOM";

    // Power block references
    private static Element powerProviderBlock = null;
    private static Element powerConsumerBlock = null;
    private static List<String> powerProviderProps = new ArrayList<>();
    private static List<String> powerConsumerProps = new ArrayList<>();

    // Cache for element properties to improve performance
    private static final Map<String, Map<String, Object>> elementPropertiesCache = new WeakHashMap<>();

    // Cache for element property objects to avoid redundant lookups
    private static final Map<String, Map<String, Property>> elementPropertyObjectsCache = new WeakHashMap<>();

    // Element indexing
    private static final Map<String, Map<String, Element>> elementNameIndexes = new ConcurrentHashMap<>();
    private static final Map<String, Element> elementIdIndex = new ConcurrentHashMap<>();

    // Room blocks cache for performance optimization
    private static final Map<String, Class> roomBlocksCache = new HashMap<>();

    // Room element cache for optimized property and connector collection
    private static final Map<String, RoomElementData> roomElementCache = new HashMap<>();

    /**
     * Data container for room elements (properties and connectors)
     * Optimized to separate cabinet properties from non-cabinet properties during collection
     */
    public static class RoomElementData {
        private final Map<String, Property> cabinetProperties = new HashMap<>();
        private final Map<String, Property> nonCabinetProperties = new HashMap<>();
        private final List<Element> connectors = new ArrayList<>();

        public Map<String, Property> getCabinetProperties() {
            return new HashMap<>(cabinetProperties);
        }

        public Map<String, Property> getNonCabinetProperties() {
            return new HashMap<>(nonCabinetProperties);
        }

        public List<Element> getConnectors() {
            return new ArrayList<>(connectors);
        }

        void addCabinetProperty(String cabinetName, Property property) {
            cabinetProperties.put(cabinetName, property);
        }

        void addNonCabinetProperty(String propertyName, Property property) {
            nonCabinetProperties.put(propertyName, property);
        }

        void addConnector(Element connector) {
            connectors.add(connector);
        }
    }

    //--------------------------------------------------------------------------
    // 2. POWER CLASSIFIERS
    //--------------------------------------------------------------------------
    // Top-level classifier names in IDP Taxonomy
    public static final String CLASSIFIER_POWER_PROVIDER = "Power Provider";
    public static final String CLASSIFIER_POWER_CONSUMER = "Power Consumer";
    public static final String CLASSIFIER_ROOM = "Room";

    // Connection elements
    private static final Set<String> CONNECTOR_CLASSIFIERS = new HashSet<>(Arrays.asList(
            "Cable", "Connector", "Transceiver", "Cable component", "Cable label", "Receptacle"
    ));

    // Cabinet elements
    private static final Set<String> CABINET_CLASSIFIERS = new HashSet<>(Arrays.asList(
            "Cabinet"
    ));

    // Cache for classifier type determinations to improve performance
    private static final Map<String, String> classifierTypeCache = new HashMap<>();

    // Cache for subclassifier relationship checks (key format: "subclass|parentClass")
    private static final Map<String, Boolean> subclassifierRelationshipCache = new HashMap<>();

    /**
     * Determines the power type a classifier by checking its position in the
     * IDP Taxonomy hierarchy. For power providers and consumers, uses hierarchy detection.
     * For connectors and cabinets, directly checks the classifier sets.
     *
     * @param classifier The classifier name to check
     * @return The power type (TYPE_POWER_PROVIDER, TYPE_POWER_CONSUMER, etc.) or null
     */
    public static String getPowerType(String classifier) {
        if (classifier == null) {
            return null;
        }

        // Check cache first for performance
        if (classifierTypeCache.containsKey(classifier)) {
            String cachedType = classifierTypeCache.get(classifier);
            return cachedType;
        }

        String type = null;

        // Check connector and cabinet lists directly
        if (CONNECTOR_CLASSIFIERS.contains(classifier)) {
            type = TYPE_CONNECTOR;
        } else if (CABINET_CLASSIFIERS.contains(classifier)) {
            type = TYPE_CABINET;
        }
        // Use hierarchy check for power providers and consumers
        else if (isSubclassifier(classifier, CLASSIFIER_POWER_PROVIDER)) {
            type = TYPE_POWER_PROVIDER;
        } else if (isSubclassifier(classifier, CLASSIFIER_POWER_CONSUMER)) {
            type = TYPE_POWER_CONSUMER;
        }

        // Cache result for future use
        classifierTypeCache.put(classifier, type);
        return type;
    }

    /**
     * Checks if a classifier is a subclassifier of a parent classifier in the IDP Taxonomy.
     * Uses cached IDP Taxonomy package for improved performance.
     * Results are cached to avoid repeated hierarchy traversals.
     *
     * @param classifier The classifier to check
     * @param parentClassifier The potential parent classifier
     * @return true if classifier is a subclassifier of parentClassifier
     */
    private static boolean isSubclassifier(String classifier, String parentClassifier) {
        // Direct equality check
        if (classifier.equals(parentClassifier)) {
            return true;
        }

        // Check cache first
        String cacheKey = classifier + "|" + parentClassifier;
        if (subclassifierRelationshipCache.containsKey(cacheKey)) {
            boolean cachedResult = subclassifierRelationshipCache.get(cacheKey);
            return cachedResult;
        }

        Project project = SysMLStereotypes.getProject();
        if (project == null) {
            return false;
        }

        // Get the IDP Taxonomy package from cache
        Package idpTaxonomyPkg = SysMLStereotypes.getIDPTaxonomyPackage();
        if (idpTaxonomyPkg == null) {
           Log("Hierarchy check - IDP Taxonomy package not found");
            return false;
        }

        // Find the classifier element
        Element classifierElement = findClassifierElement(idpTaxonomyPkg, classifier);
        if (classifierElement == null) {
            Log("Hierarchy check - " + classifier + " not found in taxonomy");
            subclassifierRelationshipCache.put(cacheKey, false);
            return false;
        }

        // Find the parent classifier element
        Element parentElement = findClassifierElement(idpTaxonomyPkg, parentClassifier);
        if (parentElement == null) {
            Log("Hierarchy check - " + parentClassifier + " not found in taxonomy");
            subclassifierRelationshipCache.put(cacheKey, false);
            return false;
        }

        // Check if classifier is a subclass of parentClassifier
        boolean result = isSubclass(classifierElement, parentElement);

        // Cache the result
        subclassifierRelationshipCache.put(cacheKey, result);

        return result;
    }

    /**
     * Finds a classifier element by name in the IDP Taxonomy package.
     *
     * @param taxonomyPackage The IDP Taxonomy package
     * @param classifierName The name of the classifier to find
     * @return The classifier element or null if not found
     */
    private static Element findClassifierElement(Package taxonomyPackage, String classifierName) {
        for (Element element : taxonomyPackage.getOwnedElement()) {
            if (element instanceof NamedElement &&
                    ((NamedElement)element).getName().equals(classifierName)) {
                return element;
            }
        }
        return null;
    }

    /**
     * Checks if element is a subclass of potentialParent by traversing the generalization hierarchy.
     *
     * @param element The element to check
     * @param potentialParent The potential parent element
     * @return true if element is a subclass of potentialParent
     */
    private static boolean isSubclass(Element element, Element potentialParent) {
        if (element == null || potentialParent == null) {
            return false;
        }

        // Direct equality check
        if (element.equals(potentialParent)) {
            return true;
        }

        // Check generalizations if element is a classifier
        if (element instanceof Classifier) {Classifier classifier = (Classifier) element;

            for (Generalization gen : classifier.getGeneralization()) {
                Element general = gen.getGeneral();
                if (general != null) {
                    if (general.equals(potentialParent) || isSubclass(general, potentialParent)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Finds and caches all room blocks in the Location View package
     * @param project The project to search in
     * @return Map of room names to their Class objects
     */
    public static Map<String, Class> findAndCacheRoomBlocks(Project project) {
        // Return existing cache if already populated
        if (!roomBlocksCache.isEmpty()) {
            return new HashMap<>(roomBlocksCache);
        }

        if (project == null) {
            Log("Cannot find room blocks: project is null");
            return new HashMap<>();
        }

        // Get the Location View package
        Package locationPackage = SysMLStereotypes.getLocationViewPackage(project);
        if (locationPackage == null) {
            Log("Location View package not found. Room caching skipped.");
            return new HashMap<>();
        }

        // Build indexes for the location package if not already indexed
        buildIndexes(locationPackage);

        // Single iteration to find all room blocks and populate room element cache
        initializeRoomElementCache(locationPackage);

        return new HashMap<>(roomBlocksCache);
    }

    /**
     * Initializes room element cache by processing all room blocks once
     * Collects both cabinet properties and connectors in a single pass
     */
    private static synchronized void initializeRoomElementCache(Package locationPackage) {
        // Skip if already initialized (thread-safe check)
        if (!roomElementCache.isEmpty()) {
            return;
        }

        roomBlocksCache.clear();

        // Single iteration to find all room blocks and collect their elements
        for (Element element : locationPackage.getOwnedElement()) {
            // Skip association relationships
            if (element instanceof Association) {
                continue;
            }

            if (element instanceof Class && element instanceof NamedElement) {
                // Check if it's a Room block by examining generalizations directly
                if (element instanceof Classifier) {
                    Classifier classifier = (Classifier) element;

                    for (Generalization gen : classifier.getGeneralization()) {
                        Element general = gen.getGeneral();

                        if (general instanceof NamedElement) {
                            String generalName = ((NamedElement) general).getName();
                            if ("Room".equalsIgnoreCase(generalName)) {
                                String roomName = ((NamedElement) element).getName();
                                Class roomBlock = (Class) element;

                                // Cache the room block
                                roomBlocksCache.put(roomName, roomBlock);

                                // Process room elements in single pass
                                RoomElementData roomData = new RoomElementData();

                                if (Utilities.ModelElements.isBlock(roomBlock)) {
                                    for (Element owned : roomBlock.getOwnedElement()) {
                                        if (owned instanceof Property) {
                                            Property property = (Property) owned;
                                            String propertyName = property.getName();

                                            if (propertyName != null && property.getType() instanceof Class) {
                                                Class propertyType = (Class) property.getType();
                                                String typeName = propertyType.getName();
                                                if (typeName != null) {
                                                    ClassifierInfo classifierInfo = Utilities.ModelElements.findBaseClassifierInfo(propertyType);
                                                    String classifierName = null;
                                                    if (classifierInfo != null) {
                                                        classifierName = classifierInfo.getClassifierName();
                                                    } else {
                                                        Log("Room element classification - Property '" + propertyName + "' with type '" + typeName +
                                                            "' could not be resolved to IDP Taxonomy classifier, using type name");
                                                    }

                                                    String nameForClassification = (classifierName != null) ? classifierName : typeName;

                                                    // Use ConnectionRegistry's elementTypeMap if available, fallback to getPowerType()
                                                    String powerType = null;
                                                    ConnectionRegistry registry = ConnectionRegistry.getInstance();
                                                    if (registry.isModelAnalyzed()) {
                                                        // Use the pre-analyzed element type map for better performance
                                                        powerType = registry.getElementType(propertyName);
                                                    }

                                                    // Fallback to getPowerType() if not found in registry (for backward compatibility)
                                                    if (powerType == null) {
                                                        powerType = getPowerType(nameForClassification);
                                                    }

                                                    if (TYPE_CABINET.equals(powerType)) {
                                                        roomData.addCabinetProperty(propertyName, property);
                                                    } else if (TYPE_POWER_PROVIDER.equals(powerType)) {
                                                        roomData.addNonCabinetProperty(propertyName, property);
                                                    } else if (TYPE_POWER_CONSUMER.equals(powerType)) {
                                                        roomData.addNonCabinetProperty(propertyName, property);
                                                    }
                                                }
                                            }
                                        }

                                        // Collect connectors
                                        if (owned.getHumanType() != null &&
                                                owned.getHumanType().equals("Connector")) {
                                            roomData.addConnector(owned);
                                        }
                                    }
                                }

                                roomElementCache.put(roomName, roomData);

                                // Make sure the room is indexed for future lookups
                                Utilities.indexElement(element);
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Gets all connectors from cached room data
     */
    public static List<Element> getAllRoomConnectors() {
        if (roomElementCache.isEmpty()) {
            // Force initialization if not done yet
            Project project = SysMLStereotypes.getProject();
            if (project != null) {
                findAndCacheRoomBlocks(project);
            } else {
                return new ArrayList<>();
            }
        }

        List<Element> allConnectors = new ArrayList<>();
        for (RoomElementData roomData : roomElementCache.values()) {
            allConnectors.addAll(roomData.getConnectors());
        }
        return allConnectors;
    }

    /**
     * Gets room names from the cache, finding and caching room blocks if not already done
     * @param project The project
     * @return Set of room names
     */
    public static Set<String> getRoomNamesFromCache(Project project) {
        findAndCacheRoomBlocks(project);
        return new HashSet<>(roomBlocksCache.keySet());
    }

    /**
     * Gets cabinet properties for a specific room from the cache
     * @param roomName The room name
     * @return Map of cabinet names to Property objects, or empty map if room not found
     */
    public static Map<String, Property> getRoomCabinetProperties(String roomName) {
        if (roomName == null) {
            return new HashMap<>();
        }

        RoomElementData roomData = roomElementCache.get(roomName);
        if (roomData != null) {
            return roomData.getCabinetProperties();
        }

        return new HashMap<>();
    }

    /**
     * Gets non-cabinet properties for a specific room from the cache
     * @param roomName The room name
     * @return Map of property names to Property objects, or empty map if room not found
     */
    public static Map<String, Property> getRoomNonCabinetProperties(String roomName) {
        if (roomName == null) {
            return new HashMap<>();
        }

        RoomElementData roomData = roomElementCache.get(roomName);
        if (roomData != null) {
            return roomData.getNonCabinetProperties();
        }

        return new HashMap<>();
    }

    /**
     * Gets connectors for a specific room from the cache
     * @param roomName The room name
     * @return List of connector elements, or empty list if room not found
     */
    public static List<Element> getRoomConnectors(String roomName) {
        if (roomName == null) {
            return new ArrayList<>();
        }

        RoomElementData roomData = roomElementCache.get(roomName);
        if (roomData != null) {
            return roomData.getConnectors();
        }

        return new ArrayList<>();
    }

    /**
     * Finds and caches Power Provider and Power Consumer blocks and their properties.
     * This method should be called during project initialization.
     */
    public static void findPowerBlocks() {
        Package idpTaxonomyPkg = SysMLStereotypes.getIDPTaxonomyPackage();
        if (idpTaxonomyPkg == null) {
            Log("Failed to find IDP Taxonomy package");
            return;
        }

        // Build indexes for the taxonomy package to speed up element lookup
        buildIndexes(idpTaxonomyPkg);

        // Find Power Provider block
        powerProviderBlock = findClassifierElement(idpTaxonomyPkg, CLASSIFIER_POWER_PROVIDER);
        if (powerProviderBlock != null) {
            powerProviderProps = getPropertyNamesFromBlock(powerProviderBlock);
            // Index the power provider block
            Utilities.indexElement(powerProviderBlock);
        } else {
            Log("Failed to find Power Provider block");
        }

        // Find Power Consumer block
        powerConsumerBlock = findClassifierElement(idpTaxonomyPkg, CLASSIFIER_POWER_CONSUMER);
        if (powerConsumerBlock != null) {
            powerConsumerProps = getPropertyNamesFromBlock(powerConsumerBlock);
            // Index the power consumer block
            Utilities.indexElement(powerConsumerBlock);
        } else {
            Log("Failed to find Power Consumer block");
        }
    }

    /**
     * Gets property names from a block element.
     * @param block The block element
     * @return List of property names
     */
    private static List<String> getPropertyNamesFromBlock(Element block) {
        List<String> propNames = new ArrayList<>();

        if (block != null) {
            for (Element owned : block.getOwnedElement()) {
                if (owned instanceof Property) {
                    Property prop = (Property) owned;

                    // Skip constraint properties
                    if (!isConstraintProperty(prop)) {
                        propNames.add(prop.getName());
                    }
                }
            }
        }

        return propNames;
    }

    /**
     * Gets the cached Power Provider block
     * @return The Power Provider block element
     */
    public static Element getPowerProviderBlock() {
        return powerProviderBlock;
    }

    /**
     * Gets the cached Power Consumer block
     * @return The Power Consumer block element
     */
    public static Element getPowerConsumerBlock() {
        return powerConsumerBlock;
    }

    /**
     * Checks if a property is a constraint property.
     *
     * @param property The property to check
     * @return true if it's a constraint property, false otherwise
     */
    private static boolean isConstraintProperty(Property property) {
        Type type = property.getType();
        return (type != null && ModelElements.isConstraintBlock(type));
    }

    //--------------------------------------------------------------------------
    // 3. LOGGING FUNCTIONS
    //--------------------------------------------------------------------------
    /**
     * Logs a message to the MagicDraw console.
     * @param arg The object to log
     */
    public static void Log(Object arg) {
        Application.getInstance().getGUILog().log(String.valueOf(arg));
    }

    /**
     * Shows a project switch notification dialog.
     * @param previousProjectName Name of the previous project
     * @param newProjectName Name of the new project
     */
    public static void showProjectSwitchNotification(String previousProjectName, String newProjectName) {
        JOptionPane.showMessageDialog(
                Application.getInstance().getMainFrame(),
                "To avoid data inconsistencies, please close project '" + newProjectName + "' before going back to project '" + previousProjectName + "'.\n\n",
                "Project Switch Warning",
                JOptionPane.WARNING_MESSAGE
        );
    }

    //--------------------------------------------------------------------------
    // 4. MODEL ELEMENT UTILITIES
    //--------------------------------------------------------------------------
    /**
     * Inner class containing model element utilities.
     */
    public static class ModelElements {
        // Project-specific caches to avoid cross-repository issues
        private static Map<String, Map<String, String>> projectClassifierCache = new HashMap<>();
        private static Map<String, Map<String, Map<String, Property>>> projectPropertyHierarchyCache = new HashMap<>();
        private static Map<String, Map<String, Property>> projectPropertyLookupCache = new HashMap<>();

        // Legacy caches - kept for backward compatibility but no longer used
        private static Map<String, String> classifierCache = new HashMap<>();
        private static Map<String, Map<String, Property>> allPropertyHierarchyCache = new HashMap<>();
        private static Map<String, Property> propertyHierarchyLookupCache = new HashMap<>();

        //--------------------------------------------------------------------------
        // 4a. GETTERS & FINDERS
        //--------------------------------------------------------------------------
        /**
         * Get the project-specific classifier cache
         */
        private static Map<String, String> getClassifierCache(Project project) {
            if (project == null) {
                return new HashMap<>(); // Return empty map if project is null
            }

            String projectId = project.getID();
            if (!projectClassifierCache.containsKey(projectId)) {
                projectClassifierCache.put(projectId, new HashMap<>());
            }
            return projectClassifierCache.get(projectId);
        }

        /**
         * Get the project-specific property lookup cache
         */
        private static Map<String, Property> getPropertyLookupCache(Project project) {
            if (project == null) {
                return new HashMap<>(); // Return empty map if project is null
            }

            String projectId = project.getID();
            if (!projectPropertyLookupCache.containsKey(projectId)) {
                projectPropertyLookupCache.put(projectId, new HashMap<>());
            }
            return projectPropertyLookupCache.get(projectId);
        }

        /**
         * Recursively finds a package by name within another package.
         * @param pkg The package to search in
         * @param packageName The name of the package to find
         * @return The found package or null if not found
         */
        public static Package findPackageByName(Package pkg, String packageName) {
            if (pkg.getName().equals(packageName)) {
                return pkg;
            }

            for (Element element : pkg.getOwnedMember()) {
                if (element instanceof Package) {
                    Package packages = (Package) element;
                    if (packages.getName().equals(packageName)) {
                        return packages;
                    } else {
                        Package subPackage = findPackageByName(packages, packageName);
                        if (subPackage != null) {
                            return subPackage;
                        }
                    }
                }
            }
            return null;
        }

        /**
         * Finds a property by name in an element.
         * @param element The element to search in
         * @param propertyName The name of the property to find
         * @return The found property or null if not found
         */
        public static Property findPropertyByName(Element element, String propertyName) {
            for (Element owned : element.getOwnedElement()) {
                if (owned instanceof Property &&
                        propertyName.equals(((Property) owned).getName())) {
                    return (Property) owned;
                }
            }
            return null;
        }

        /**
         * Helper method to find an element by name in the model
         * @param project The project
         * @param elementName The name of the element to find
         * @return The element or null if not found
         */
        public static Element findElementByName(Project project, String elementName) {
            Package assetsPackage = SysMLStereotypes.getAssetsViewPackage(project);
            if (assetsPackage != null) {
                Element element = findElementByNameInPackage(assetsPackage, elementName);
                if (element != null) {
                    return element;
                }
            }

            return null;
        }

        /**
         * Helper method to find an element by name in a package (recursive)
         * @param pkg The package to search in
         * @param elementName The name of the element to find
         * @return The element or null if not found
         */
        public static Element findElementByNameInPackage(Package pkg, String elementName) {
            for (Element element : pkg.getOwnedElement()) {
                if (element instanceof NamedElement && elementName.equals(((NamedElement) element).getName())) {
                    return element;
                } else if (element instanceof Package) {
                    Element found = findElementByNameInPackage((Package) element, elementName);
                    if (found != null) {
                        return found;
                    }
                }
            }
            return null;
        }

        /**
         * Finds connectors in a package and its subpackages
         * @param pkg The package to search in
         * @param connectors List to store found connectors
         */
        public static void findConnectors(Package pkg, List<Element> connectors) {
            // Collect packages and blocks for batch processing
            List<Package> subPackages = new ArrayList<>();
            List<Class> blockClasses = new ArrayList<>();

            // First pass: categorize elements
            for (Element element : pkg.getOwnedElement()) {
                if (element instanceof Package) {
                    subPackages.add((Package)element);
                } else if (element instanceof Class) {
                    // Only check for connectors if the class has Block stereotype
                    if (isBlock(element)) {
                        blockClasses.add((Class)element);
                    }
                }
            }

            // Process blocks in batches
            final int BATCH_SIZE = 50;
            for (int i = 0; i < blockClasses.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, blockClasses.size());
                List<Class> batch = blockClasses.subList(i, endIndex);

                // Process this batch of blocks
                for (Class block : batch) {
                    for (Element owned : block.getOwnedElement()) {
                        if (owned.getHumanType() != null && owned.getHumanType().equals("Connector")) {
                            connectors.add(owned);
                        }
                    }
                }
            }

            // Process subpackages recursively
            for (Package subPkg : subPackages) {
                ModelElements.findConnectors(subPkg, connectors);
            }
        }

        /**
         * Finds the base classifier for a class.
         * @param element The class to check
         * @return ClassifierInfo containing the classifier name and Product Catalog element, or null if not found
         */
        public static ClassifierInfo findBaseClassifierInfo(Class element) {
            if (element == null) {
                return null;
            }

            try {
                // Get the current project
                Project project = SysMLStereotypes.getProject();
                if (project == null) {
                    Log("Warning: No active project in findBaseClassifierInfo");
                    return null;
                }

                // Log element name for debugging
                String elementName = element instanceof NamedElement ?
                        ((NamedElement)element).getName() : "unnamed";

                // Get project-specific cache
                Map<String, String> cache = getClassifierCache(project);

                // Check if element is valid
                try {
                    String elementId = element.getID();
                    if (elementId == null || elementId.isEmpty()) {
                        Log("Warning: Element has invalid ID in findBaseClassifierInfo");
                        return null;
                    }
                } catch (Exception e) {
                    Log("Warning: Element appears to be from a different repository in findBaseClassifierInfo");
                    return null;
                }

                Element partsCatalogElement = null;
                String result = null;

                try {
                    for (Element rel : element.get_relationshipOfRelatedElement()) {
                        if (rel instanceof Generalization) {
                            Generalization gen = (Generalization) rel;
                            Element target = gen.getGeneral();
                            if (target != null && target.getOwner() instanceof NamedElement) {
                                String ownerName = ((NamedElement)target.getOwner()).getName();
                                if (ownerName.equals(PACKAGE_PRODUCT_CATALOG)) {
                                    partsCatalogElement = target;
                                    break;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    Log("Warning: Error accessing relationships in findBaseClassifierInfo: " + e.getMessage());
                    return null;
                }

                // If we found a Product Catalog element, check cache first
                if (partsCatalogElement instanceof NamedElement) {
                    String partsCatalogName = ((NamedElement)partsCatalogElement).getName();

                    if (cache.containsKey(partsCatalogName)) {
                        result = cache.get(partsCatalogName);
                        return new ClassifierInfo(result, partsCatalogElement);
                    }

                    // Not in cache, check its generalizations
                    try {
                        for (Element rel : partsCatalogElement.get_relationshipOfRelatedElement()) {
                            if (rel instanceof Generalization) {
                                Generalization gen = (Generalization) rel;
                                Element target = gen.getGeneral();
                                if (target != null && target.getOwner() instanceof NamedElement &&
                                        ((NamedElement) target.getOwner()).getName().equals(PACKAGE_IDP_TAXONOMY)) {
                                    result = ((NamedElement)target).getName();
                                    break;
                                }
                            }
                        }
                    } catch (Exception e) {
                        Log("Warning: Error accessing generalizations in findBaseClassifierInfo: " + e.getMessage());
                        return null;
                    }

                    // Store in project-specific cache
                    cache.put(partsCatalogName, result);
                }

                if (result == null) {
                    Log("Base classifier - No taxonomy classifier found for: " + elementName);
                    return null;
                }

                return new ClassifierInfo(result, partsCatalogElement);
            } catch (Exception e) {
                Log("Error in findBaseClassifierInfo: " + e.getMessage());
                return null;
            }
        }

        /**
         * Finds multiple properties in the class inheritance hierarchy with a single traversal.
         * @param classElement The class to check
         * @param propertyNames Set of property names to find
         * @return Map of property names to Property objects
         */
        public static Map<String, Property> findPropertiesInHierarchy(Class classElement, Set<String> propertyNames) {
            Map<String, Property> result = new HashMap<>();
            if (classElement == null || propertyNames == null || propertyNames.isEmpty()) {
                return result;
            }

            // Get the current project
            Project project = SysMLStereotypes.getProject();
            if (project == null) {
                Log("Warning: No active project in findPropertiesInHierarchy");
                return result;
            }

            // Verify the class element is valid
            try {
                String elementId = classElement.getID();
                if (elementId == null || elementId.isEmpty()) {
                    Log("Warning: Class element has invalid ID in findPropertiesInHierarchy");
                    return result;
                }

                // Get project-specific cache
                Map<String, Property> cache = getPropertyLookupCache(project);

                // Check cache first for each property
                Set<String> uncachedProps = new HashSet<>();
                for (String propName : propertyNames) {
                    String cacheKey = elementId + "_" + propName;
                    if (cache.containsKey(cacheKey)) {
                        Property cachedProp = cache.get(cacheKey);
                        if (cachedProp != null) {
                            try {
                                // Validate cached property
                                String propId = cachedProp.getID();
                                if (propId != null && !propId.isEmpty()) {
                                    result.put(propName, cachedProp);
                                    continue;
                                }
                            } catch (Exception e) {
                                // Invalid cached property
                                cache.remove(cacheKey);
                            }
                        } else {
                            // Null result was cached, skip
                            continue;
                        }
                    }
                    uncachedProps.add(propName);
                }

                // If all properties were in cache, return early
                if (uncachedProps.isEmpty()) {
                    return result;
                }

                // Check direct properties first
                try {
                    for (Element owned : classElement.getOwnedElement()) {
                        if (owned instanceof Property) {
                            Property prop = (Property)owned;
                            String propName = prop.getName();
                            if (uncachedProps.contains(propName)) {
                                result.put(propName, prop);
                                cache.put(elementId + "_" + propName, prop);
                                uncachedProps.remove(propName);

                                // If we found all properties, return early
                                if (uncachedProps.isEmpty()) {
                                    return result;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    Log("Warning: Error accessing owned elements in batch property lookup: " + e.getMessage());
                }

                // Check parent classes for remaining properties
                try {
                    for (Generalization gen : classElement.getGeneralization()) {
                        if (gen.getGeneral() instanceof Class) {
                            Class parent = (Class)gen.getGeneral();

                            // Verify parent is valid before recursing
                            try {
                                String parentId = parent.getID();
                                if (parentId != null && !parentId.isEmpty()) {
                                    Map<String, Property> parentProps = findPropertiesInHierarchy(parent, uncachedProps);

                                    // Add found properties to result and cache
                                    for (Map.Entry<String, Property> entry : parentProps.entrySet()) {
                                        String propName = entry.getKey();
                                        Property prop = entry.getValue();

                                        result.put(propName, prop);
                                        cache.put(elementId + "_" + propName, prop);
                                        uncachedProps.remove(propName);
                                    }

                                    // If we found all properties, return early
                                    if (uncachedProps.isEmpty()) {
                                        return result;
                                    }
                                }
                            } catch (Exception e) {
                                Log("Warning: Parent class is invalid in batch property lookup: " + e.getMessage());
                            }
                        }
                    }
                } catch (Exception e) {
                    Log("Warning: Error accessing generalizations in batch property lookup: " + e.getMessage());
                }

                // Cache null results for unfound properties
                for (String propName : uncachedProps) {
                    cache.put(elementId + "_" + propName, null);
                }

            } catch (Exception e) {
                Log("Warning: Class element appears to be from a different repository in batch property lookup: " + e.getMessage());
            }

            return result;
        }

        /**
         * Gets the value of a property.
         * @param property The property to get the value from
         * @return The property value or null if not found
         */
        public static Object getPropertyValue(Property property) {
            if (property == null || property.getDefaultValue() == null) {
                return null;
            }

            ValueSpecification value = property.getDefaultValue();
            if (value instanceof LiteralReal) {
                return ((LiteralReal) value).getValue();
            } else if (value instanceof LiteralString) {
                return ((LiteralString) value).getValue();
            } else if (value instanceof LiteralInteger) {
                return ((LiteralInteger) value).getValue();
            }

            return null;
        }

        /**
         * Gets cached property objects for an element.
         * @param element The element to get property objects for
         * @return Map of property names to Property objects, or null if not found
         */
        public static Map<String, Property> getCachedPropertyObjects(Element element) {
            if (element == null) {
                return null;
            }

            try {
                String elementId = element.getID();
                if (elementId != null && !elementId.isEmpty() && elementPropertyObjectsCache.containsKey(elementId)) {
                    // Return a copy of the cached properties - validation will be done when actually using them
                    return new HashMap<>(elementPropertyObjectsCache.get(elementId));
                }
            } catch (Exception e) {
                Log("Warning: Error getting cached property objects: " + e.getMessage());
            }

            return null;
        }

        /**
         * Stores property objects in the cache for later use.
         * @param element The element that owns the properties
         * @param propertyObjects Map of property names to Property objects
         */
        public static void storePropertyObjects(Element element, Map<String, Property> propertyObjects) {
            if (element == null || propertyObjects == null || propertyObjects.isEmpty()) {
                return;
            }

            try {
                String elementId = element.getID();
                if (elementId != null && !elementId.isEmpty()) {
                    elementPropertyObjectsCache.put(elementId, new HashMap<>(propertyObjects));
                }
            } catch (Exception e) {
                Log("Warning: Error storing property objects: " + e.getMessage());
            }
        }

        /**
         * Collects essential property values and Property objects for an element.
         * @param element The element to collect properties from
         * @param powerType The power type of the element
         * @param productCatalogElement The Product Catalog element (if available) to optimize property lookup
         * @return PropertyCollection containing both values and Property objects
         */
        public static PropertyCollection collectPropertiesWithObjects(Element element, String powerType, Element productCatalogElement) {
            // Check element properties cache first
            try {
                if (element instanceof Class) {
                    Class classElement = (Class) element;
                    String elementId = classElement.getID();

                    // Check cache first
                    if (elementPropertiesCache.containsKey(elementId)) {
                        Map<String, Object> cachedValues = elementPropertiesCache.get(elementId);
                        return new PropertyCollection(new HashMap<>(cachedValues), new HashMap<>());
                    }
                }
            } catch (Exception e) {
                Log("Warning: Error checking element properties cache: " + e.getMessage());
            }

            PropertyCollection properties = new PropertyCollection();

            if (element instanceof Class) {
                Class classElement = (Class) element;

                // Determine which properties to collect
                Set<String> propsToCollect = new HashSet<>();
                if (TYPE_POWER_PROVIDER.equals(powerType) && !powerProviderProps.isEmpty()) {
                    propsToCollect.addAll(powerProviderProps);
                    propsToCollect.add(PROP_HOST_LOCATION);
                } else if (TYPE_POWER_CONSUMER.equals(powerType) && !powerConsumerProps.isEmpty()) {
                    propsToCollect.addAll(powerConsumerProps);
                }
                propsToCollect.add(PROP_HOST_ASSET);

                Map<String, Property> foundProps;

                // If we have a Product Catalog element, check it first for direct properties
                if (productCatalogElement instanceof Class) {
                    // First check direct properties in the Product Catalog element
                    Map<String, Property> catalogProps = new HashMap<>();
                    Set<String> remainingProps = new HashSet<>(propsToCollect);

                    try {
                        for (Element owned : productCatalogElement.getOwnedElement()) {
                            if (owned instanceof Property) {
                                Property prop = (Property)owned;
                                String propName = prop.getName();
                                if (propsToCollect.contains(propName)) {
                                    catalogProps.put(propName, prop);
                                    remainingProps.remove(propName);
                                }
                            }
                        }
                    } catch (Exception e) {
                        Log("Warning: Error accessing Product Catalog properties: " + e.getMessage());
                    }

                    // Only search the hierarchy for remaining properties
                    if (!remainingProps.isEmpty()) {
                        Map<String, Property> hierarchyProps = findPropertiesInHierarchy(classElement, remainingProps);
                        // Combine the results
                        foundProps = new HashMap<>(catalogProps);
                        foundProps.putAll(hierarchyProps);
                    } else {
                        foundProps = catalogProps;
                    }
                } else {
                    // Use batch lookup to find all properties in the hierarchy
                    foundProps = findPropertiesInHierarchy(classElement, propsToCollect);
                }

                // Process found properties
                for (Map.Entry<String, Property> entry : foundProps.entrySet()) {
                    String propName = entry.getKey();
                    Property prop = entry.getValue();

                    if (PROP_HOST_ASSET.equals(propName) && prop.getType() instanceof NamedElement) {
                        properties.addProperty(propName, ((NamedElement)prop.getType()).getName(), prop);
                    } else if (PROP_HOST_LOCATION.equals(propName) && prop.getType() instanceof NamedElement) {
                        properties.addProperty(propName, ((NamedElement)prop.getType()).getName(), prop);
                    } else {
                        Object value = getPropertyValue(prop);
                        properties.addProperty(propName, value, prop);
                    }
                }

                // Cache the result (only the values, not the Property objects)
                try {
                    String elementId = classElement.getID();
                    elementPropertiesCache.put(elementId, new HashMap<>(properties.getPropertyValues()));
                } catch (Exception e) {
                    Log("Warning: Error caching element properties: " + e.getMessage());
                }
            }

            return properties;
        }

        //--------------------------------------------------------------------------
        // 4b. BOOLEAN CHECKERS
        //--------------------------------------------------------------------------
        /**
         * Checks if an element is a constraint block.
         * @param element The element to check
         * @return true if the element is a constraint block
         */
        public static boolean isConstraintBlock(Element element) {
            if (!(element instanceof Class)) {
                return false;
            }

            Stereotype constraintBlockStereotype = SysMLStereotypes.getConstraintBlockStereotype();
            return StereotypesHelper.hasStereotype(element, constraintBlockStereotype);
        }

        /**
         * Checks if an element is a block.
         * @param element The element to check
         * @return true if the element is a block
         */
        public static boolean isBlock(Element element) {
            Stereotype blockStereotype = StereotypesHelper.getStereotype(
                    SysMLStereotypes.getProject(),
                    STEREOTYPE_BLOCK,
                    SysMLStereotypes.getSysMLProfile());
            return StereotypesHelper.hasStereotype(element, blockStereotype);
        }

        /**
         * Checks if an element is a Room block.
         * @param element The element to check
         * @return true if the element is a Room block
         */
        public static boolean isRoom(Element element) {
            // First check if it's a block
            if (!isBlock(element)) {
                return false;
            }

            // Check if it's a Room block by directly examining generalizations
            if (element instanceof Classifier) {
                Classifier classifier = (Classifier) element;

                for (Generalization gen : classifier.getGeneralization()) {
                    Element general = gen.getGeneral();

                    if (general instanceof NamedElement) {
                        String generalName = ((NamedElement) general).getName();
                        if ("Room".equalsIgnoreCase(generalName)) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        //--------------------------------------------------------------------------
        // 4c. MAIN CATEGORIZATION FUNCTION
        //--------------------------------------------------------------------------
        /**
         * Categorizes power elements based on their classifiers with cabinet property caching.
         * @param project The project
         * @param elementPropsMap Map to store element properties
         * @param cabinets Set to store cabinet elements
         * @param cabinetBlocksMap Map to store cabinet block references
         * @return Map of element names to their power types
         */
        public static Map<String, String> categorizePowerElements(Project project, Map<String, Map<String, Object>> elementPropsMap,
                                                                  Set<String> cabinets, Map<String, Class> cabinetBlocksMap) {
            Map<String, String> elementTypeMap = new HashMap<>();

            Package assetsPackage = SysMLStereotypes.getAssetsViewPackage(project);
            if (assetsPackage != null) {
                // Build indexes for the assets package to speed up element lookup
                buildIndexes(assetsPackage);

                // Recursively categorize assets directly into the elementTypeMap
                categorizeAssetsRecursively(assetsPackage, elementTypeMap, elementPropsMap, cabinets, cabinetBlocksMap);
            }

            return elementTypeMap;
        }

        /**
         * Recursively categorizes assets in a package using batch processing with cabinet property caching.
         * @param package_ The package to search in
         * @param elementTypeMap Map to store element types
         * @param elementPropsMap Map to store element properties
         * @param cabinets Set to store cabinet elements
         * @param cabinetBlocksMap Map to store cabinet block references (can be null)
         */
        private static void categorizeAssetsRecursively(Package package_, Map<String, String> elementTypeMap,
                                                      Map<String, Map<String, Object>> elementPropsMap, Set<String> cabinets,
                                                      Map<String, Class> cabinetBlocksMap) {
            // Collect all elements first to enable batch processing
            List<Element> blockElements = new ArrayList<>();

            // Single pass through elements, categorizing each appropriately
            for (Element element : package_.getOwnedElement()) {
                if (element instanceof Package) {
                    categorizeAssetsRecursively((Package)element, elementTypeMap, elementPropsMap, cabinets, cabinetBlocksMap);
                } else if (element instanceof Association) {
                    // Skip associations relationships
                    continue;
                } else if (element instanceof Class && isBlock(element)) {
                    blockElements.add(element);
                }
            }

            // Process blocks in batches of 100
            final int BATCH_SIZE = 100;
            for (int i = 0; i < blockElements.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, blockElements.size());
                List<Element> batch = blockElements.subList(i, endIndex);

                // Pre-fetch classifiers and product catalog elements for the batch
                Map<Element, String> batchClassifiers = new HashMap<>();
                Map<Element, Element> batchProductCatalogElements = new HashMap<>();
                for (Element element : batch) {
                    if (element instanceof Class) {
                        ClassifierInfo classifierInfo = findBaseClassifierInfo((Class)element);
                        if (classifierInfo != null) {
                            String classifier = classifierInfo.getClassifierName();
                            Element productCatalogElement = classifierInfo.getProductCatalogElement();
                            batchClassifiers.put(element, classifier);
                            if (productCatalogElement != null) {
                                batchProductCatalogElements.put(element, productCatalogElement);
                            }
                        }
                    }
                }

                // Process the batch
                for (Element element : batch) {
                    if (element instanceof NamedElement) {
                        String name = ((NamedElement)element).getName();
                        String classifier = batchClassifiers.get(element);

                        if (classifier != null) {
                            // Categorize the element
                            String powerType = getPowerType(classifier);
                            Element productCatalogElement = batchProductCatalogElements.get(element);

                            if (TYPE_POWER_PROVIDER.equals(powerType)) {
                                elementTypeMap.put(name, TYPE_POWER_PROVIDER);
                                PropertyCollection propCollection = collectPropertiesWithObjects(element, powerType, productCatalogElement);
                                elementPropsMap.put(name, propCollection.getPropertyValues());
                                storePropertyObjects(element, propCollection.getPropertyObjects());
                            } else if (TYPE_POWER_CONSUMER.equals(powerType)) {
                                elementTypeMap.put(name, TYPE_POWER_CONSUMER);
                                PropertyCollection propCollection = collectPropertiesWithObjects(element, powerType, productCatalogElement);
                                elementPropsMap.put(name, propCollection.getPropertyValues());
                                storePropertyObjects(element, propCollection.getPropertyObjects());
                            } else if (TYPE_CONNECTOR.equals(powerType)) {
                                elementTypeMap.put(name, TYPE_CONNECTOR);
                            } else if (TYPE_CABINET.equals(powerType)) {
                                cabinets.add(name);
                                if (element instanceof Class) {
                                    Class cabinetBlock = (Class) element;

                                    // Store cabinet block reference
                                    if (cabinetBlocksMap != null) {
                                        cabinetBlocksMap.put(name, cabinetBlock);
                                    }

                                    PropertyCollection propCollection = new PropertyCollection();
                                    String hostLocation = null;

                                    for (Property property : cabinetBlock.getOwnedAttribute()) {
                                        propCollection.addProperty(property.getName(), null, property);

                                        // Check if this is the host_location property
                                        if ("host_location".equals(property.getName()) && property.getType() instanceof NamedElement) {
                                            hostLocation = ((NamedElement) property.getType()).getName();
                                        }
                                    }

                                    // Store the collected properties in the cache
                                    CabinetProperties.cacheCabinetPropertyCollection(name, propCollection);

                                    // Store the host location if found
                                    if (hostLocation != null) {
                                        CabinetProperties.cacheCabinetHostLocation(name, hostLocation);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        /**
         * Clears caches for a specific project.
         * @param project The project to clear caches for
         */
        public static void clearProjectCaches(Project project) {
            if (project == null) {
                return;
            }

            String projectId = project.getID();
            projectClassifierCache.remove(projectId);
            projectPropertyHierarchyCache.remove(projectId);
            projectPropertyLookupCache.remove(projectId);
        }
    }

    //--------------------------------------------------------------------------
    // 5. ELEMENT INDEXING
    //--------------------------------------------------------------------------
    /**
     * Indexes an element by ID and name for fast lookup.
     * @param element The element to index
     */
    public static void indexElement(Element element) {
        if (element == null) return;

        try {
            // Index by ID
            String elementId = element.getID();
            if (elementId != null && !elementId.isEmpty()) {
                elementIdIndex.put(elementId, element);
            }

            // Index by name if it's a named element
            if (element instanceof NamedElement) {
                String name = ((NamedElement)element).getName();
                if (name != null && !name.isEmpty()) {
                    // Get the project ID for project-specific name indexing
                    Project project = SysMLStereotypes.getProject();
                    String projectId = project != null ? project.getID() : "default";

                    // Create project index if it doesn't exist
                    elementNameIndexes.computeIfAbsent(projectId, k -> new ConcurrentHashMap<>())
                        .put(name, element);
                }
            }
        } catch (Exception e) {
            Log("Error indexing element: " + e.getMessage());
        }
    }

    /**
     * Finds an element by ID using the index.
     * @param elementId The ID of the element to find
     * @return The found element or null if not found
     */
    public static Element findElementById(String elementId) {
        if (elementId == null || elementId.isEmpty()) return null;
        return elementIdIndex.get(elementId);
    }

    /**
     * Finds an element by name using the index.
     * @param name The name of the element to find
     * @return The found element or null if not found
     */
    public static Element findElementByName(String name) {
        if (name == null || name.isEmpty()) return null;

        // Get the current project
        Project project = SysMLStereotypes.getProject();
        String projectId = project != null ? project.getID() : "default";

        // Check project-specific index
        Map<String, Element> projectIndex = elementNameIndexes.get(projectId);
        if (projectIndex != null) {
            return projectIndex.get(name);
        }

        return null;
    }

    /**
     * Builds indexes for all elements in a package and its subpackages.
     * @param rootPackage The root package to index
     * @return The number of elements indexed
     */
    public static int buildIndexes(Package rootPackage) {
        int count = buildIndexesRecursively(rootPackage);
        return count;
    }

    /**
     * Recursively builds indexes for all elements in a package and its subpackages.
     * @param pkg The package to index
     * @return The number of elements indexed
     */
    private static int buildIndexesRecursively(Package pkg) {
        if (pkg == null) return 0;

        int count = 0;

        // Index the package itself
        Utilities.indexElement(pkg);
        count++;

        // Index all owned elements
        for (Element element : pkg.getOwnedElement()) {
            Utilities.indexElement(element);
            count++;

            // Recursively index subpackages
            if (element instanceof Package) {
                count += buildIndexesRecursively((Package)element);
            }
        }

        return count;
    }

    /**
     * Clears all element indexes.
     */
    public static void clearIndexes() {
        elementIdIndex.clear();
        elementNameIndexes.clear();
    }

    //--------------------------------------------------------------------------
    // 6. CABINET PROPERTIES UTILITIES
    //--------------------------------------------------------------------------
    /**
     * Inner class containing cabinet properties utilities.
     */
    public static class CabinetProperties {
        // Cabinet properties cache - single source of truth
        private static final Map<String, PropertyCollection> cabinetPropertiesCache = new HashMap<>();
        private static final Map<String, String> cabinetHostLocationCache = new HashMap<>();

        /**
         * Gets cabinet properties from the cache
         * @param cabinetName The cabinet name
         * @return Map of property names to Property objects, or null if not found
         */
        public static Map<String, Property> getCabinetPropertiesFromCache(String cabinetName) {
            PropertyCollection propertyCollection = cabinetPropertiesCache.get(cabinetName);
            if (propertyCollection != null) {
                Map<String, Property> propertyObjects = propertyCollection.getPropertyObjects();
                if (propertyObjects != null && !propertyObjects.isEmpty()) {
                    return propertyObjects;
                }
            }
            return null;
        }

        /**
         * Caches cabinet property collection for faster lookup
         * @param cabinetName The cabinet name
         * @param propertyCollection The property collection to cache
         */
        public static void cacheCabinetPropertyCollection(String cabinetName, PropertyCollection propertyCollection) {
            if (cabinetName != null && propertyCollection != null) {
                cabinetPropertiesCache.put(cabinetName, propertyCollection);
            }
        }

        /**
         * Caches cabinet host location for faster lookup
         * @param cabinetName The cabinet name
         * @param hostLocation The host location (room name)
         */
        public static void cacheCabinetHostLocation(String cabinetName, String hostLocation) {
            if (cabinetName != null && hostLocation != null && !hostLocation.isEmpty()) {
                cabinetHostLocationCache.put(cabinetName, hostLocation);
            }
        }

        /**
         * Gets cabinet host location from the cache
         * @param cabinetName The cabinet name
         * @return Host location (room name) or null if not found
         */
        public static String getCabinetHostLocationFromCache(String cabinetName) {
            return cabinetHostLocationCache.get(cabinetName);
        }

        /**
         * Clears all cabinet properties caches
         */
        public static void clearCaches() {
            cabinetPropertiesCache.clear();
            cabinetHostLocationCache.clear();
        }
    }

    //--------------------------------------------------------------------------
    // 7. CACHING UTILITIES
    //--------------------------------------------------------------------------
    /**
     * Clears all caches.
     */
    public static void clearCaches() {
        // Clear legacy caches
        ModelElements.classifierCache.clear();
        ModelElements.allPropertyHierarchyCache.clear();
        ModelElements.propertyHierarchyLookupCache.clear();

        // Clear project-specific caches
        ModelElements.projectClassifierCache.clear();
        ModelElements.projectPropertyHierarchyCache.clear();
        ModelElements.projectPropertyLookupCache.clear();

        // Clear classifier caches
        classifierTypeCache.clear();
        subclassifierRelationshipCache.clear();

        // Clear power block caches
        powerProviderBlock = null;
        powerConsumerBlock = null;
        powerProviderProps.clear();
        powerConsumerProps.clear();

        // Clear element properties cache
        elementPropertiesCache.clear();
        elementPropertyObjectsCache.clear();

        // Clear room blocks cache
        roomBlocksCache.clear();

        // Clear room element cache
        roomElementCache.clear();

        // Clear cabinet properties caches
        CabinetProperties.clearCaches();

        // Clear element indexes
        clearIndexes();

        // Also clear for current project specifically
        Project currentProject = SysMLStereotypes.getProject();
        if (currentProject != null) {
            ModelElements.clearProjectCaches(currentProject);
        }
    }
}