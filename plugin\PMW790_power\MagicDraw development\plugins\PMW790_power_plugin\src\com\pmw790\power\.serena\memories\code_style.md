# Code Style and Conventions

## Naming Conventions
- **Classes**: PascalCase (e.g., `PowerDiagramManager`, `BindingContext`)
- **Methods**: camelCase (e.g., `createParametricDiagram`, `analyzeModelConnections`)
- **Variables**: camelCase (e.g., `adjacencyMap`, `elementTypeMap`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `DIAGRAM_TYPE`, `LOG_PREFIX`)
- **Packages**: lowercase with dots (e.g., `com.pmw790.power.diagram`)

## Documentation
- **JavaDoc Comments**: Used for public methods and classes
- **Comment Style**: Descriptive comments explaining complex logic
- **Section Headers**: Comments with dashed lines are used to separate logical sections in longer files

## Code Organization
- **Singletons**: Many manager classes use the singleton pattern with private constructors and `getInstance()` methods
- **Utility Classes**: Static utility methods are organized in inner classes (e.g., `Utilities.ModelElements`)
- **Context Classes**: Used to pass related data between methods instead of having many parameters

## Error Handling
- **Logging**: Uses `Utilities.Log()` for user-visible messages
- **Exception Handling**: Try-catch blocks with specific error handling and recovery strategies
- **Fail Gracefully**: Operations check pre-conditions and fail gracefully with informative error messages

## Performance Optimization
- **Caching**: Extensive use of caching for model elements, stereotypes, and properties
- **Batch Processing**: Operations on collections are often batched for better performance
- **Cache Invalidation**: Clear caches when the project changes or closes

## MagicDraw/Cameo Integration
- **UI Integration**: Uses the MagicDraw browser context menu API for integration
- **Event Listeners**: Uses ProjectEventListenerAdapter for project lifecycle events
- **Sessions**: Uses SessionManager for model modification operations

## Other Conventions
- **Private Methods First**: Helper methods are typically placed after the public methods they support
- **Method Grouping**: Related methods are grouped together with section headers
- **Null Checks**: Defensive programming with null checks before operations
- **Collection Immutability**: Return unmodifiable collections when exposing internal state
