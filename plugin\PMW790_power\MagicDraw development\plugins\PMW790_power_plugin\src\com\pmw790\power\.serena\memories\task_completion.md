# Task Completion Guidelines

## When a Task is Complete

### Code Review Checklist
- [ ] Code follows the naming conventions and style guidelines
- [ ] All public methods and classes have JavaDoc comments
- [ ] Error handling is implemented appropriately
- [ ] Cache management is properly implemented where needed
- [ ] No unnecessary object creation that could impact performance
- [ ] Changes are compatible with MagicDraw/Cameo API

### Testing
Since this is a MagicDraw/Cameo plugin, testing is primarily manual:
1. Build the plugin
2. Install in MagicDraw/Cameo
3. Verify functionality through user interface
4. Test with various model configurations

### Performance Considerations
- Minimize object creation in loops
- Use caching appropriately
- Clear caches when no longer needed
- Use batch operations when working with collections

### Documentation
- Update JavaDoc as needed
- Document complex algorithms and logic
- Include parameter descriptions for public methods

### Building the Plugin
As specific build instructions aren't visible in the codebase, consult the project's build documentation.

### Deployment
1. Build the plugin JAR
2. Place the JAR in MagicDraw's "plugins" directory
3. Restart MagicDraw/Cameo
4. Verify plugin functionality

### Lifecycle Management
- Ensure proper initialization in the `init()` method
- Ensure proper cleanup in the `close()` method
- Handle project open/close events appropriately
- Properly manage state during project switches

### SysML Compatibility
- Ensure code works with SysML profile elements
- Verify stereotype applications are correct
- Test with various SysML block types and connections
