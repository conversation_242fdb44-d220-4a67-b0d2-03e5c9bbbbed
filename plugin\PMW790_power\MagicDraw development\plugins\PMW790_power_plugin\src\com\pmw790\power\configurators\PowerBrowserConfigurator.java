package com.pmw790.power.configurators;

import com.nomagic.actions.ActionsManager;
import com.nomagic.magicdraw.actions.BrowserContextAMConfigurator;
import com.nomagic.magicdraw.ui.browser.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.actions.ActionsCategory;

public class PowerBrowserConfigurator implements BrowserContextAMConfigurator {
	public static final PowerBrowserConfigurator INSTANCE = new PowerBrowserConfigurator();

	// In PowerBrowserConfigurator.java, modify the configure method:

	@Override
	public void configure(ActionsManager manager, Tree browserTree) {
		// Get the selected node
		Object selectedNode = browserTree.getSelectedNode();
		if (!(selectedNode instanceof Node)) {
			return;
		}

		Node node = (Node) selectedNode;
		Object userObject = node.getUserObject();
		if (!(userObject instanceof Element)) {
			return;
		}

		Element element = (Element) userObject;

		// Create the main category
		ActionsCategory powerCategory = new ActionsCategory("PMW790_POWER_TOOLS", "PMW790 Power Tools");
		powerCategory.setNested(true);

		// Create the power tools category as an action
		ActionsCategory powerToolsCategory = new ActionsCategory("POWER_TOOLS_CATEGORY", "Power Tools");
		powerToolsCategory.setNested(true);

		// Add our actions to the power tools category
		PowerFolderAction PowerActions = new PowerFolderAction(element);
		ReanalyzeConnectionsAction ReAnalyze = new ReanalyzeConnectionsAction(element);

		powerToolsCategory.addAction(PowerActions);
		powerToolsCategory.addAction(ReAnalyze);

		// Add the power tools category to the main category
		powerCategory.addAction(powerToolsCategory);

		// Add the main category to the context menu
		manager.addCategory(powerCategory);
	}

	@Override
	public int getPriority() {
		return 10;
	}
}