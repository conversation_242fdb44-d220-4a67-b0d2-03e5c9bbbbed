# PMW790 Power Plugin for MagicDraw/Cameo

## Purpose
This is a MagicDraw/Cameo Systems Modeler plugin designed to analyze and visualize power connections in SysML models. The plugin provides tools for:

1. Creating parametric diagrams for power analysis of cabinets and rooms
2. Analyzing power connections between providers and consumers
3. Displaying power-related properties like voltage, current, and power consumption
4. Creating constraint blocks for power calculations

## Core Functionality
- Power connection analysis between providers and consumers
- Creation of SysML parametric diagrams for power analysis
- Automated binding of power-related values to constraint blocks
- Visualization of power relationships within cabinets and rooms

## Integration
The plugin integrates with MagicDraw/Cameo Systems Modeler through:
- Browser context menu actions
- Model analysis capabilities
- Diagram creation and manipulation

## Target Users
Engineers and systems designers using MagicDraw/Cameo who need to:
- Analyze power connections in their system models
- Visualize power relationships
- Ensure proper power distribution in their designs
