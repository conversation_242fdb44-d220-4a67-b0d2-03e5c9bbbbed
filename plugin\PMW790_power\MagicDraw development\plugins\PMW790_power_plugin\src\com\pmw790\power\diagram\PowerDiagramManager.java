package com.pmw790.power.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.openapi.uml.ModelElementsManager;
import com.nomagic.magicdraw.openapi.uml.ReadOnlyElementException;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.layout.ClassDiagramLayouter;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.pmw790.power.functions.ConnectionRegistry;
import com.pmw790.power.functions.SysMLStereotypes;

import java.util.Collection;

import static com.pmw790.power.functions.Utilities.Log;

/**
 * Manages power diagram creation and manipulation for parametric diagrams
 * including creation of constraint blocks and binding connectors.
 */
public class PowerDiagramManager {

    // Constants for diagram creation
    private static final String DIAGRAM_TYPE = "SysML Parametric Diagram";
    private static final String TIMESTAMP_FORMAT = "yyyyMMdd_HHmmss";
    private static final String DIAGRAM_NAME_PATTERN = "%s_Power Analysis %s";

    /**
     * Creates a parametric diagram for power analysis of the selected element
     * Handles both cabinet blocks and room blocks
     *
     * @param project The MagicDraw project
     * @param selectedElement The selected block (cabinet or room)
     */
    public static void createParametricDiagram(Project project, Element selectedElement) {
        if (!SysMLStereotypes.ensureInitialized()) {
            Log("Error: SysMLStereotypes initialization failed. Cannot create parametric diagram.");
            return;
        }

        if (!(selectedElement instanceof Class)) {
            Log("No valid Block element selected for diagram creation. Selected: " +
                    (selectedElement != null ? selectedElement.getHumanType() : "null"));
            return;
        }
        Class blockElement = (Class) selectedElement;

        // Ensure the connection registry is up to date
        ConnectionRegistry registry = ConnectionRegistry.getInstance();
        if (!registry.isModelAnalyzed()) {
            Log("Model connections not analyzed. Analyzing now...");
            registry.analyzeModelConnections(project);
            Log("Model analysis complete.");
        }

        // Check if the selected element is a Room block or Cabinet block using the registry's lists
        String elementName = ((Class) selectedElement).getName();
        if (registry.getRooms().contains(elementName)) {
            // Handle room block
            createRoomParametricDiagram(project, blockElement, registry);
        } else if (registry.getCabinets().contains(elementName)) {
            // Handle cabinet block
            createCabinetParametricDiagram(project, blockElement, registry);
        } else {
            Log("Error: Selected element is not recognized as a room or as a cabinet in the model");
            return;
        }
    }

    /**
     * Creates a parametric diagram for power analysis of a room block
     * This will include all cabinets in the room
     *
     * @param project The MagicDraw project
     * @param roomBlock The selected room block
     * @param registry The connection registry
     */
    private static void createRoomParametricDiagram(Project project, Class roomBlock, ConnectionRegistry registry) {
        String roomName = roomBlock.getName();

        // Create the room diagram context - this will handle all cabinets in the room
        RoomDiagramContext roomContext = new RoomDiagramContext(project, roomBlock, registry);

        // Check if there are any cabinets in this room
        if (roomContext.getCabinetCount() == 0) {
            Log("No cabinets found in room: " + roomName);
            return;
        }

        // Find or update parametric diagram
        DiagramPresentationElement diagram = findOrUpdateParametricDiagram(project, roomBlock);
        if (diagram == null) {
            Log("Error: Could not create or update diagram for room: " + roomName);
            return;
        }

        try {
            roomContext.addToRoomDiagram(diagram);

            for (CabinetDiagramContext cabinetContext : roomContext.getAllCabinetContexts()) {
                try {
                    Class cabinetBlock = cabinetContext.getCabinetBlock();
                    if (cabinetBlock != null) {
                        // Check if providers exist for this cabinet before attempting to create diagram
                        if (cabinetContext.getProviders().isEmpty()) {
                            Log("Skipping parametric diagram creation for cabinet " + cabinetContext.getCabinetName() + " - no power providers found");
                            continue;
                        }
                        
                        // Try to create/update the cabinet diagram
                        createCabinetParametricDiagram(project, cabinetBlock, registry);
                    }
                } catch (Exception e) {
                    Log("Error processing cabinet " + cabinetContext.getCabinetName() + ": " + e.getMessage());
                    // Continue with other cabinets
                }
            }
            
            // Apply layout to the diagram using the common method
            applyDiagramLayout(diagram);

            diagram.open();
//            Log("Room parametric diagram creation complete for: " + roomName);
//            Log("=".repeat(80));

        } catch (ReadOnlyElementException roe) {
            Log("Error: Read-only element encountered during diagram creation: " + roe.getMessage());
            roe.printStackTrace();
        } catch (Exception e) {
            Log("Error creating parametric diagram for room " + roomName + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Creates a parametric diagram for power analysis of the selected cabinet element
     *
     * @param project The MagicDraw project
     * @param cabinetBlock The selected cabinet block
     * @param registry The connection registry
     */
    private static void createCabinetParametricDiagram(Project project, Class cabinetBlock, ConnectionRegistry registry) {
        try {
            // Create the context object - this performs initial lookups
            CabinetDiagramContext context = new CabinetDiagramContext(project, cabinetBlock, registry);
            String cabinetName = context.getCabinetName();

            // Check if the selected element is a valid cabinet according to the registry
            if (cabinetName == null || !registry.getCabinets().contains(cabinetName)) {
                Log("Error: Selected element '" + (cabinetName == null ? "null" : cabinetName) +
                    "' is not recognized as a cabinet in the connection registry.");
                return;
            }

            // Check if providers exist for this cabinet
            if (context.getProviders().isEmpty()) {
                Log("Warning: No power providers found for cabinet: " + cabinetName + ". Parametric diagram not created.");
                return;
            }

            // Find or update parametric diagram
            DiagramPresentationElement diagram = findOrUpdateParametricDiagram(project, cabinetBlock);
            if (diagram == null) {
                Log("Error: Could not create or update diagram for cabinet: " + cabinetName);
                return;
            }

            // Add power asset parts (providers and consumers)
            PowerDiagramElementManager.addPowerAssetsToDiagram(diagram, context);

            // Add constraint properties to the diagram
            PowerDiagramElementManager.addConstraintPropertiesToDiagram(diagram, context);

            // Create binding connectors between constraint blocks and power properties
            PowerConnectorManager.createBindingConnectors(context, diagram);

            // Apply layout to the diagram using the common method
            applyDiagramLayout(diagram);

            diagram.open();
//            Log("Parametric diagram creation complete for: " + cabinetName);
//            Log("=".repeat(80));

        } catch (ReadOnlyElementException roe) {
             Log("Error: Read-only element encountered during diagram creation: " + roe.getMessage());
             roe.printStackTrace();
        } catch (Exception e) {
            Log("Error creating parametric diagram for " + cabinetBlock.getName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Finds existing parametric diagram for an element or creates a new one
     * Deletes and recreates existing diagrams rather than trying to update them
     * 
     * @param project The project
     * @param blockElement The block element (cabinet or room)
     * @return The diagram presentation element, or null if creation failed
     */
    private static DiagramPresentationElement findOrUpdateParametricDiagram(Project project, Class blockElement) {
        String blockName = blockElement.getName();
        DiagramPresentationElement existingDiagram = null;
        
        // Find existing diagram
        Collection<DiagramPresentationElement> diagrams = project.getDiagrams();
        for (DiagramPresentationElement diagram : diagrams) {
            com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Diagram diagramElement = 
                    (com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Diagram) diagram.getElement();
            
            if (diagramElement != null && 
                diagramElement.getOwner() == blockElement &&
                diagram.getDiagramType().getType().equals(DIAGRAM_TYPE)) {
                
                existingDiagram = diagram;
                break;
            }
        }
        
        if (existingDiagram != null) {
            // Delete existing diagram completely
            try {
                ModelElementsManager.getInstance().removeElement(existingDiagram.getElement());
            } catch (Exception e) {
                Log("Error deleting existing diagram: " + e.getMessage());
            }
        }
        
        // Create a new diagram
        try {
            String timestamp = new java.text.SimpleDateFormat(TIMESTAMP_FORMAT)
                    .format(new java.util.Date());
            String diagramName = String.format(DIAGRAM_NAME_PATTERN, blockName, timestamp);
            
            com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Diagram paramDiagram =
                    ModelElementsManager.getInstance().createDiagram(DIAGRAM_TYPE, blockElement);
            paramDiagram.setName(diagramName);
            
            return project.getDiagram(paramDiagram);
        } catch (Exception e) {
            Log("Error creating parametric diagram for " + blockName + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Applies standard layout to a diagram with fallback options
     */
    private static void applyDiagramLayout(DiagramPresentationElement diagram) {
        try {
            diagram.layout(true);

            // Schedule a second layout pass on the UI thread after a short delay
            javax.swing.SwingUtilities.invokeLater(() -> {
                try {
                    diagram.layout(true, new ClassDiagramLayouter()); // Use alternate layout algorithm
                } catch (Exception e) {
                    // Silently handle layout exceptions
                }
            });
        } catch (Exception e) {
            Log("Warning: Could not apply layout: " + e.getMessage());
            try {
                // Fallback to standard layout
                diagram.layout(true);
            } catch (Exception ex) {
                // If even the fallback fails, just continue
                Log("Fallback layout also failed: " + ex.getMessage());
            }
        }
    }
}