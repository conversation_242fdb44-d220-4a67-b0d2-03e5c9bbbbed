# Suggested Shell Commands

## Windows Commands for Development

### Directory Navigation and File Management
- `dir` - List files and directories
- `cd <directory>` - Change directory
- `mkdir <directory>` - Create directory
- `copy <source> <destination>` - Copy files
- `move <source> <destination>` - Move/rename files
- `del <file>` - Delete files
- `rmdir /s /q <directory>` - Remove directory and contents

### Search and Find
- `findstr /s /i "<pattern>" *.java` - Search for pattern in Java files (case-insensitive)
- `dir /s /b *.java` - Find all Java files recursively

### Git Commands
- `git status` - Check status of repository
- `git add .` - Stage all changes
- `git commit -m "message"` - Commit changes
- `git push` - Push changes to remote
- `git pull` - Pull changes from remote
- `git branch` - List branches
- `git checkout -b <branch-name>` - Create and switch to new branch
- `git merge <branch-name>` - Merge branch into current branch

### MagicDraw Development
- MagicDraw/Cameo plugins typically use <PERSON><PERSON> or <PERSON><PERSON> for building
- Since specific build commands aren't visible in the codebase, consult the plugin's documentation

### Development
- `javac -d <out-dir> -classpath <classpath> <source-files>` - Compile Java files
- `jar cf <jar-file> -C <class-dir> .` - Create JAR file

### Testing
This is a MagicDraw plugin without visible testing setup in the code. Testing would typically involve:
1. Installing the plugin in MagicDraw/Cameo
2. Testing the plugin's functionality manually through the UI

### Deployment
To deploy a MagicDraw/Cameo plugin:
1. Build the plugin JAR file
2. Copy to MagicDraw's "plugins" directory
3. Restart MagicDraw/Cameo
