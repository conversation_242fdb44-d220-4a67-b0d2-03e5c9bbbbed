package com.pmw790.power.functions;
import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.jmi.helpers.StereotypesHelper;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Package;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Type;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Stereotype;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.NamedElement;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Profile;
import com.nomagic.magicdraw.core.Application;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class SysMLStereotypes {
    // Primary caches
    private static Project projectCache;
    private static Profile sysmlProfileCache;

    // Package caches
    private static Package idpResourcesLayoutPackageCache;
    private static Package idpTaxonomyPackageCache;
    private static Package assetsViewPackageCache;
    private static Package locationViewPackageCache;

    // Constraint blocks cache
    private static Map<String, Class> constraintBlocksCache = new ConcurrentHashMap<>();

    // Stereotype caches
    private static Stereotype constraintParameterStereotype = null;
    private static Stereotype nestedConnectorEndStereotype = null;
    private static Stereotype constraintBlockStereotype = null;
    private static Stereotype valuePropertyStereotype = null;

    // Type caches
    private static Map<Project, Map<String, Element>> primitiveTypeCache = new HashMap<>();
    private static Map<String, Type> iso80000TypeCache = new HashMap<>();

    public static void initialize(Project project) {
        // Cache the project
        projectCache = project;

        // Find and cache the SysML profile
        sysmlProfileCache = StereotypesHelper.getProfile(project, "SysML");

        // Find and cache the ConstraintParameter stereotype during initialization
        Package mdCustomization = getMDCustomizationForSysML(project);
        if (mdCustomization != null) {
            constraintParameterStereotype = findConstraintParameterStereotype(mdCustomization);
        }

        // Cache the NestedConnectorEnd stereotype
        nestedConnectorEndStereotype = StereotypesHelper.getStereotype(project, "NestedConnectorEnd", sysmlProfileCache);

        // Cache the ConstraintBlock stereotype
        constraintBlockStereotype = StereotypesHelper.getStereotype(project, "ConstraintBlock", sysmlProfileCache);

        // Cache the ValueProperty stereotype
        valuePropertyStereotype = StereotypesHelper.getStereotype(project, "ValueProperty");

        // Find and cache IDP Resources Layout package
        idpResourcesLayoutPackageCache = findIDPResourcesLayoutPackage(project);

        // Find and cache IDP Taxonomy package
        idpTaxonomyPackageCache = findIDPTaxonomyPackage(project);

        // Cache Power Calculation constraint blocks
        cachePowerCalculationConstraintBlocks(project);

        // Find and cache Assets View package
        assetsViewPackageCache = findAssetsViewPackage(project);

        // Find and cache Location View package
        locationViewPackageCache = findLocationViewPackage(project);

        // Preload ISO80000 types
        preloadISO80000Types(project);
    }

    /** GETTER METHODS */
    public static Project getProject() {
        if (projectCache == null) {
            projectCache = Application.getInstance().getProject();
        }
        return projectCache;
    }

    /**
     * Checks if SysMLStereotypes are initialized for the current project
     */
    public static boolean ensureInitialized() {
        try {
            Project currentProject = Application.getInstance().getProject();

            // Check if we need to initialize
            boolean needsInitialization = false;

            if (projectCache == null || currentProject == null) {
                needsInitialization = true;
            } else if (projectCache != currentProject) {
                needsInitialization = true;
            } else if (sysmlProfileCache == null) {
                needsInitialization = true;
            } else if (constraintParameterStereotype == null || nestedConnectorEndStereotype == null) {
                needsInitialization = true;
            }

            // Initialize if needed
            if (needsInitialization && currentProject != null) {
                initialize(currentProject);

                // Verify initialization was successful
                if (sysmlProfileCache == null) {
                    return false;
                }
                return true;
            }

            return !needsInitialization;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static Profile getSysMLProfile() {
        if (sysmlProfileCache == null) {
            sysmlProfileCache = StereotypesHelper.getProfile(getProject(), "SysML");
        }
        return sysmlProfileCache;
    }

    public static Stereotype getNestedConnectorEndStereotype() {
        if (nestedConnectorEndStereotype == null) {
            Utilities.Log("NestedConnectorEnd stereotype not initialized");}

        return nestedConnectorEndStereotype;
    }

    public static Stereotype getConstraintBlockStereotype() {
        if (constraintBlockStereotype == null) {
            Project project = getProject();
            Profile profile = getSysMLProfile();
            if (project != null && profile != null) {
                constraintBlockStereotype = StereotypesHelper.getStereotype(project, "ConstraintBlock", profile);
            }
            if (constraintBlockStereotype == null) {
                Utilities.Log("ConstraintBlock stereotype could not be initialized or found.");
            }
        }
        return constraintBlockStereotype;
    }

    /**
     * Gets the cached ValueProperty stereotype
     * @return The ValueProperty stereotype or null if not found
     */
    public static Stereotype getValuePropertyStereotype() {
        if (valuePropertyStereotype == null) {
            Project project = getProject();
            valuePropertyStereotype = StereotypesHelper.getStereotype(project, "ValueProperty");
            if (valuePropertyStereotype == null) {
                Utilities.Log("ValueProperty stereotype could not be initialized or found.");
            }
        }
        return valuePropertyStereotype;
    }

    /**
     * Gets the cached IDP Resources Layout package
     * @return The IDP Resources Layout package or null if not found
     */
    public static Package getIDPResourcesLayoutPackage() {
        if (idpResourcesLayoutPackageCache == null) {
            Project project = getProject();
            if (project != null) {
                idpResourcesLayoutPackageCache = findIDPResourcesLayoutPackage(project);
            }
        }
        return idpResourcesLayoutPackageCache;
    }

    /**
     * Gets the cached IDP Taxonomy package
     * @return The IDP Taxonomy package or null if not found
     */
    public static Package getIDPTaxonomyPackage() {
        if (idpTaxonomyPackageCache == null) {
            Project project = getProject();
            if (project != null) {
                idpTaxonomyPackageCache = findIDPTaxonomyPackage(project);
            }
        }
        return idpTaxonomyPackageCache;
    }

    /**
     * Gets the cached Assets View package
     * @return The Assets View package or null if not found
     */
    public static Package getAssetsViewPackage(Project project) {
        // If using a different project than cached, find the package
        if (project != projectCache) {
            return findAssetsViewPackage(project);
        }

        // Check if we need to initialize the cache
        if (assetsViewPackageCache == null) {
            assetsViewPackageCache = findAssetsViewPackage(project);
        }

        return assetsViewPackageCache;
    }

    /**
     * Gets the cached Location View package
     * @return The Location View package or null if not found
     */
    public static Package getLocationViewPackage(Project project) {
        if (project != projectCache) {
            return findLocationViewPackage(project);
        }

        // Check if we need to initialize the cache
        if (locationViewPackageCache == null) {
            locationViewPackageCache = findLocationViewPackage(project);
        }

        return locationViewPackageCache;
    }

    /** HELPER METHODS for CONSTRAINT PAR */
    public static Package getMDCustomizationForSysML(Project project) {
        for (Element element : project.getModels()) {
            if (element instanceof Package) {
                Package mdCustomization = Utilities.ModelElements.findPackageByName((Package) element, "MD Customization for SysML");
                if (mdCustomization != null) {
                    return mdCustomization;
                }
            }
        }
        return null;
    }

    public static Stereotype findConstraintParameterStereotype(Package mdCustomization) {
        for (Element element : mdCustomization.getOwnedElement()) {
            if (element instanceof Package && "additional_stereotypes".equals(((Package) element).getName())) {
                Package additionalStereotypes = (Package) element;
                return findStereotypeInPackage(additionalStereotypes, "ConstraintParameter");
            }
        }
        return null;
    }

    public static Stereotype findStereotypeInPackage(Package pkg, String stereotypeName) {
        for (Element element : pkg.getOwnedElement()) {
            if (element instanceof Stereotype && stereotypeName.equals(((Stereotype) element).getName())) {
                return (Stereotype) element;
            }
        }
        return null;
    }

    public static Package getSysMLLibrary(Project project) {
        Profile sysmlProfile = getSysMLProfile();
        if (sysmlProfile != null) {
            return Utilities.ModelElements.findPackageByName(sysmlProfile, "Libraries");
        }
        return null;
    }

    /**
     * Finds the IDP Resources Layout package in the model
     */
    private static Package findIDPResourcesLayoutPackage(Project project) {
        for (Element element : project.getModels()) {
            if (element instanceof Package) {
                Package resourcesLayout = Utilities.ModelElements.findPackageByName((Package) element, "IDP Resources Layout");
                if (resourcesLayout != null) {
                    return resourcesLayout;
                }
            }
        }
        return null;
    }

    /**
     * Finds the IDP Taxonomy package in the model structure
     */
    private static Package findIDPTaxonomyPackage(Project project) {
        Package resourcesLayout = getIDPResourcesLayoutPackage();
        if (resourcesLayout != null) {
            Package idpTaxonomy = Utilities.ModelElements.findPackageByName(resourcesLayout, "IDP Taxonomy");
            if (idpTaxonomy != null) {
                return idpTaxonomy;
            }
        }
        return null;
    }

    /**
     * Finds the Power Calculation package in the model structure
     */
    private static Package findPowerCalculationPackage(Project project) {
        Package resourcesLayout = getIDPResourcesLayoutPackage();
        if (resourcesLayout != null) {
            Package powerCalculation = Utilities.ModelElements.findPackageByName(resourcesLayout, "Power_Calculation");
            if (powerCalculation != null) {
                return powerCalculation;
            }
        }
        return null;
    }

    /**
     * Finds the Assets View package in the project
     */
    private static Package findAssetsViewPackage(Project project) {
        for (Element element : project.getModels()) {
            if (element instanceof Package) {
                Package assetsView = Utilities.ModelElements.findPackageByName((Package) element, "Assets View");
                if (assetsView != null) {
                    return assetsView;
                }
            }
        }
        return null;
    }

    /**
     * Finds the Location View package in the project
     */
    private static Package findLocationViewPackage(Project project) {
        for (Element element : project.getModels()) {
            if (element instanceof Package) {
                Package locationView = Utilities.ModelElements.findPackageByName((Package) element, "Location View");
                if (locationView != null) {
                    return locationView;
                }
            }
        }
        return null;
    }

    /** HELPER METHODS for Value Property Types */
    public static Element findPrimitiveType(String typeName, Project project) {
        if (!primitiveTypeCache.containsKey(project)) {
            primitiveTypeCache.put(project, new HashMap<>());
        }

        Map<String, Element> projectCache = primitiveTypeCache.get(project);

        if (projectCache.containsKey(typeName)) {
            return projectCache.get(typeName);
        }

        Package sysmlLib = getSysMLLibrary(project);
        if (sysmlLib != null) {
            Package primitiveValueTypes = Utilities.ModelElements.findPackageByName(sysmlLib, "PrimitiveValueTypes");
            if (primitiveValueTypes != null) {
                for (Element element : primitiveValueTypes.getOwnedElement()) {
                    if (element instanceof NamedElement &&
                            ((NamedElement)element).getName() != null &&
                            ((NamedElement)element).getName().equals(typeName)) {
                        projectCache.put(typeName, element);
                        return element;
                    }
                }
            }
        }
        return null;
    }

    private static Package findQuantitiesPackage(Project project, String isoDomainName) {
        for (Element element : project.getModels()) {
            if (element instanceof Package) {
                Package iso80000Package = Utilities.ModelElements.findPackageByName((Package) element, "ISO-80000");
                if (iso80000Package != null) {
                    Package domainPackage = Utilities.ModelElements.findPackageByName(iso80000Package, isoDomainName);
                    if (domainPackage != null) {
                        return Utilities.ModelElements.findPackageByName(domainPackage, "Quantities");
                    }
                }
            }
        }
        return null;
    }

    public static void preloadISO80000Types(Project project) {
        // Load specific types from electromagnetism
        Package electroPackage = findQuantitiesPackage(project, "IEC80000-6 Electromagnetism");
        if (electroPackage != null) {
            loadTypesFromSpecificFolders(electroPackage, new String[]{"current linkage", "voltage"});
        }

        // Load only power from mechanics
        Package mechPackage = findQuantitiesPackage(project, "ISO80000-4 Mechanics");
        if (mechPackage != null) {
            loadTypesFromSpecificFolders(mechPackage, new String[]{"power"});
        }
    }

    private static void loadTypesFromSpecificFolders(Package quantitiesPackage, String[] targetFolders) {
        if (quantitiesPackage == null) return;

        Set<String> folderSet = Arrays.stream(targetFolders)
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        // Go through each quantity package
        for (Element owned : quantitiesPackage.getOwnedElement()) {
            if (owned instanceof Package && owned instanceof NamedElement) {
                String quantityName = ((NamedElement) owned).getName();

                if (folderSet.contains(quantityName.toLowerCase())) {
                    // Find value types with units in this quantity package
                    for (Element typeElement : ((Package) owned).getOwnedElement()) {
                        if (typeElement instanceof Type && typeElement instanceof NamedElement) {
                            String typeName = ((NamedElement) typeElement).getName();
                            iso80000TypeCache.put(typeName, (Type) typeElement);
                        }
                    }
                }
            }
        }
    }

    public static Type findISO80000Type(String typeName) {
        if (iso80000TypeCache.containsKey(typeName)) {
            return iso80000TypeCache.get(typeName);
        }

        return null;
    }

    /**
     * Caches all constraint blocks from the Power Calculation package
     */
    private static void cachePowerCalculationConstraintBlocks(Project project) {
        try {
            // Get the power calculation package and verify it exists
            Package powerCalculationPackage = findPowerCalculationPackage(project);
            if (powerCalculationPackage == null) {
                Utilities.Log("Power calculation package not found, caching aborted");
                return;
            }

            // Process elements
            for (Element element : powerCalculationPackage.getOwnedElement()) {
                try {
                    // Get the name with null check
                    String name = ((NamedElement)element).getName();
                    if (name == null || name.trim().isEmpty()) {
                        continue;
                    }

                    // Create key and cache the element
                    constraintBlocksCache.put(name, (Class)element);
                } catch (Exception elementEx) {
                    Utilities.Log("Error processing element: " + elementEx.getMessage());
                }
            }
        } catch (Exception ex) {
            Utilities.Log("Error in constraintBlocksCache: " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    /**
     * Gets all cached constraint blocks from the Power Calculation package
     * @return Map of constraint block names (normalized) to Class objects
     */
    public static Map<String, Class> getPowerCalculationConstraintBlocks() {
        if (constraintBlocksCache.isEmpty()) {
            Project project = getProject();
            if (project != null) {
                cachePowerCalculationConstraintBlocks(project);
            }
        }

        return Collections.unmodifiableMap(constraintBlocksCache);
    }

    public static void clearCaches() {
        projectCache = null;
        sysmlProfileCache = null;
        idpResourcesLayoutPackageCache = null;
        idpTaxonomyPackageCache = null;
        assetsViewPackageCache = null;
        locationViewPackageCache = null;
        constraintParameterStereotype = null;
        nestedConnectorEndStereotype = null;
        constraintBlockStereotype = null;
        valuePropertyStereotype = null;
        primitiveTypeCache.clear();
        iso80000TypeCache.clear();
        constraintBlocksCache.clear();
    }
}
