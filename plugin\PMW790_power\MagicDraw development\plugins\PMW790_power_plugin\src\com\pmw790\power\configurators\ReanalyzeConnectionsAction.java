package com.pmw790.power.configurators;

import com.nomagic.magicdraw.ui.browser.actions.DefaultBrowserAction;
import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Package;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.NamedElement;
import com.pmw790.power.functions.ConnectionRegistry;
import com.pmw790.power.functions.SysMLStereotypes;
import com.pmw790.power.functions.Utilities;

import java.awt.event.ActionEvent;
import java.util.ArrayList;
import java.util.Map;
import java.util.List;
import java.util.Set;

public class ReanalyzeConnectionsAction extends DefaultBrowserAction {
    private Element targetElement;

    public ReanalyzeConnectionsAction(Element element) {
        super("ANALYZE_BLOCK_CONNECTIONS", "Analyze Rack", null, null);
        this.targetElement = element;
    }

    @Override
    public void actionPerformed(ActionEvent e) {
        // Ensure SysMLStereotypes are initialized before proceeding
        if (!SysMLStereotypes.ensureInitialized()) {
            Utilities.Log("Error: SysMLStereotypes initialization failed. Cannot analyze connections.");
            return;
        }

        Project project = SysMLStereotypes.getProject();
        if (Utilities.ModelElements.isBlock(targetElement)) {
            // Check if the selected element is a Cabinet
            if (targetElement instanceof NamedElement) {
                String elementName = ((NamedElement) targetElement).getName();
                ConnectionRegistry registry = ConnectionRegistry.getInstance();

                // Ensure model connections are analyzed
                if (!registry.isModelAnalyzed()) {
                    Utilities.Log("Model connections not analyzed. Analyzing now...");
                    registry.analyzeModelConnections(project);
                }

                // Check if the selected element is a cabinet
                Set<String> cabinets = registry.getCabinets();
                if (cabinets.contains(elementName)) {
                    // Display cabinet analysis
                    analyzeAndDisplayCabinetPower(elementName, registry);
                }
            }
        } else {
            Utilities.Log("Not a valid block!");
        }
    }

    private void analyzeAndDisplayCabinetPower(String cabinetName, ConnectionRegistry registry) {
        Utilities.Log("Power Analysis for Cabinet: " + cabinetName);

        Map<String, Map<String, Object>> elementProps = registry.getElementProperties();

        // Find all power providers in the cabinet
        Map<String, List<String>> cabinetProviders = registry.getCabinetToPowerProviders();
        List<String> providersInCabinet = cabinetProviders.getOrDefault(cabinetName, new ArrayList<>());

        for (String providerName : providersInCabinet) {
            Utilities.Log("  Power Provider: " + providerName);

            Map<String, Object> providerProps = elementProps.get(providerName);
            if (providerProps != null) {
                displayElementProperties(providerName, providerProps);
            }

            // Find power consumers connected to this provider
            Element providerElement = findElementByName(providerName);
            if (providerElement != null) {
                Map<String, Map<String, List<String>>> connections = registry.analyzePowerRelationshipsForElement(providerElement);

                if (!connections.isEmpty()) {
                    for (Map.Entry<String, Map<String, List<String>>> entry : connections.entrySet()) {
                        String provider = entry.getKey();
                        Map<String, List<String>> connectionTypes = entry.getValue();

                        List<String> consumers = connectionTypes.get("consumers");
                        if (consumers != null && !consumers.isEmpty()) {
                            Utilities.Log("    Connected Consumers: " + String.join(", ", consumers));
                            // Display properties for each consumer
                            for (String consumerName : consumers) {
                                Map<String, Object> consumerProps = elementProps.get(consumerName);
                                if (consumerProps != null) {
                                    Utilities.Log("    Consumer: " + consumerName);
                                    displayElementProperties(consumerName, consumerProps);
                                }
                            }
                        }

                        List<String> providers = connectionTypes.get("providers");
                        if (providers != null && !providers.isEmpty()) {
                            Utilities.Log("    Connected Power Providers: " + String.join(", ", providers));
                        }
                    }
                } else {
                    Utilities.Log("    No connected power consumers found");
                }
            }
        }
    }

    private void displayElementProperties(String elementName, Map<String, Object> properties) {
        for (Map.Entry<String, Object> prop : properties.entrySet()) {
            String propName = prop.getKey();
            Object propValue = prop.getValue();


            // Format power-related properties nicely
            switch (propName) {
                case "typical_voltage":
                    Utilities.Log("      Voltage: " + propValue + " V");
                    break;
                case "operating_current":
                    Utilities.Log("      Current: " + propValue + " A");
                    break;
                case "power_consumption":
                    Utilities.Log("      Power Consume: " + propValue + " W");
                    break;
                case "max_amperage":
                    Utilities.Log("      Max Current: " + propValue + " A");
                    break;
                case "power_factor":
                    Utilities.Log("      Power Factor: " + propValue);
                    break;
            }
        }
    }

    private Element findElementByName(String name) {
        Project project = SysMLStereotypes.getProject();

        // First check the Assets View package
        Package assetsViewPackage = SysMLStereotypes.getAssetsViewPackage(project);
        if (assetsViewPackage != null) {
            Element element = findElementByNameRecursive(assetsViewPackage, name);
            if (element != null) {
                return element;
            }
        }

        // If not found, search the entire model
        for (Element model : project.getModels()) {
            Element element = findElementByNameRecursive(model, name);
            if (element != null) {
                return element;
            }
        }

        return null;
    }

    private Element findElementByNameRecursive(Element parent, String name) {
        if (parent instanceof NamedElement &&
                name.equals(((NamedElement) parent).getName())) {
            return parent;
        }

        for (Element child : parent.getOwnedElement()) {
            Element found = findElementByNameRecursive(child, name);
            if (found != null) {
                return found;
            }
        }

        return null;
    }
}