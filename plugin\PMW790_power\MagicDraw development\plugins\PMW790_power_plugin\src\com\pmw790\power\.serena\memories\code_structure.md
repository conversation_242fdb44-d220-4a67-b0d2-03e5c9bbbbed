# Code Structure Overview

The PMW790 Power Plugin is organized into several key packages:

## Package Organization

### `com.pmw790.power.main`
- `PMW790Plugin.java` - The main plugin class that initializes the plugin and manages lifecycle events

### `com.pmw790.power.configurators`
- `PowerBrowserConfigurator.java` - Configures the plugin's context menu in MagicDraw's browser
- `PowerFolderAction.java` - Action to create a parametric diagram for power analysis
- `ReanalyzeConnectionsAction.java` - Action to analyze power connections in a cabinet/rack

### `com.pmw790.power.connection`
- `ConnectionRegistry.java` - Manages model connections, stores power-related elements
- `PowerConnectionManager.java` - Analyzes power relationships between elements

### `com.pmw790.power.diagram`
- `PowerDiagramManager.java` - Creates and manages parametric diagrams
- `PowerDiagramElementManager.java` - Manages diagram elements for power diagrams
- `PowerConnectorManager.java` - Creates binding connectors between diagram elements
- `BindingContext.java` - Context class for binding operations
- `CabinetDiagramContext.java` - Context class for cabinet diagram operations
- `RoomDiagramContext.java` - Context class for room diagram operations

### `com.pmw790.power.schema`
- `BindingSchemaManager.java` - Manages binding schemas for power elements
- `BindingSchema.java` - Represents a collection of binding patterns
- `BindingPattern.java` - Represents a binding pattern between elements

### `com.pmw790.power.functions`
- `SysMLStereotypes.java` - Manages SysML stereotypes and profiles
- `Utilities.java` - Contains utility functions used throughout the plugin
- `PowerTableManager.java` - (Commented out) Would have provided table-based views

## Key Design Patterns

1. **Singleton Pattern** - Used for managers like ConnectionRegistry, PowerConnectionManager, and BindingSchemaManager
2. **Context Pattern** - Used for diagram creation through BindingContext, CabinetDiagramContext, etc.
3. **Registry Pattern** - Used for storing and retrieving model elements and connections
4. **Observer Pattern** - Used for project events through ProjectEventListenerAdapter

## Core Workflow

1. User selects a cabinet or room block in MagicDraw's browser
2. User activates one of the plugin's actions from the context menu
3. The plugin analyzes the model connections
4. The plugin creates a parametric diagram showing power relationships
5. Constraint blocks are added for power calculations
6. Binding connectors are created between power properties and constraints

## Caching Strategy

The plugin makes extensive use of caching to improve performance:
- SysML stereotypes and profiles are cached
- Model elements and their properties are cached
- Constraint blocks are cached
- Cabinet-room relationships are cached
