{"java.project.sourcePaths": ["plugins\\PMW790_power_plugin\\src"], "java.project.referencedLibraries": ["c:\\MD\\MagicDraw\\lib\\akka-actor_2.13-2.6.18.jar", "c:\\MD\\MagicDraw\\lib\\akka-slf4j_2.13-2.6.18.jar", "c:\\MD\\MagicDraw\\lib\\animal-sniffer-annotations-1.9.jar", "c:\\MD\\MagicDraw\\lib\\annotation-1.11.jar", "c:\\MD\\MagicDraw\\lib\\annotations-3.0.0.jar", "c:\\MD\\MagicDraw\\lib\\asm-7.0.jar", "c:\\MD\\MagicDraw\\lib\\batik-anim-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-awt-util-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-bridge-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-codec-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-constants-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-css-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-dom-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-ext-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-extension-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-gui-util-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-gvt-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-i18n-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-parser-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-rasterizer-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-rasterizer-ext-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-script-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-shared-resources-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-slideshow-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-squiggle-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-squiggle-ext-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-svgbrowser-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-svg-dom-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-svggen-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-svgpp-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-svgrasterizer-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-swing-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-transcoder-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-ttf2svg-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-util-1.14.jar", "c:\\MD\\MagicDraw\\lib\\batik-xml-1.14.jar", "c:\\MD\\MagicDraw\\lib\\bcprov-jdk15on-1.68.jar", "c:\\MD\\MagicDraw\\lib\\brand.jar", "c:\\MD\\MagicDraw\\lib\\brand_api.jar", "c:\\MD\\MagicDraw\\lib\\CATMBSESerialization-R2023xGA.v202210071830.jar", "c:\\MD\\MagicDraw\\lib\\checker-qual-3.12.0.jar", "c:\\MD\\MagicDraw\\lib\\classpath.jar", "c:\\MD\\MagicDraw\\lib\\cmof-1.4.jar", "c:\\MD\\MagicDraw\\lib\\com.dassault_systemes.catia.mbse.esi.md-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.dassault_systemes.catia.mbse.esi-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.dassault_systemes.modeler.foundation-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.ci.admin.lock.test.metamodel-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.ci.binary-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.ci.metamodel.project-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.ci.metamodel.querytest-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.ci.persistence.local-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.ci.persistence-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.ci.services-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.ci-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.announcement.client-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.announcement.msg-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.api.messages-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.api-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.client.core-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.common-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.config-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.core.msg-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.emf-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.helper-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.indexer.client-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.indexer.msg-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.net-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.query.client-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.query.msg-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.resourceupdate-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.serializer.akka-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.serializer-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.services.api-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.services.client-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.services.common-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.services.supervisor-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.esi.stream-2022.1.0.v20221207-0920.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.globalusages.common-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.ce-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.core.diagram-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.core.project.options-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.elementreferenceintext-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.esi.binary.metamodel-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.esi.esiproject-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.fileattachments-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.foundation-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.importexport-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.modeling-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.security-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.magicdraw.uml2-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.text-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.umlresources-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\com.nomagic.utils-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\common-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\commons-codec-1.9.jar", "c:\\MD\\MagicDraw\\lib\\commons-collections4-4.4.jar", "c:\\MD\\MagicDraw\\lib\\commons-compress-1.21.jar", "c:\\MD\\MagicDraw\\lib\\commons-io-2.11.0.jar", "c:\\MD\\MagicDraw\\lib\\commons-lang3-3.12.0.jar", "c:\\MD\\MagicDraw\\lib\\commons-logging-1.2.jar", "c:\\MD\\MagicDraw\\lib\\commons-math3-3.6.1.jar", "c:\\MD\\MagicDraw\\lib\\commons-text-1.10.0.jar", "c:\\MD\\MagicDraw\\lib\\config-1.3.0.jar", "c:\\MD\\MagicDraw\\lib\\decryptor-2022x-e59cbba6.jar", "c:\\MD\\MagicDraw\\lib\\eccpresso-all-11.14.jar", "c:\\MD\\MagicDraw\\lib\\ehcache-2.10.1.jar", "c:\\MD\\MagicDraw\\lib\\error_prone_annotations-2.7.1.jar", "c:\\MD\\MagicDraw\\lib\\failureaccess-1.0.1.jar", "c:\\MD\\MagicDraw\\lib\\FcsClient-R2022xGA.jar", "c:\\MD\\MagicDraw\\lib\\freehep-base-1.0.0.jar", "c:\\MD\\MagicDraw\\lib\\freehep-graphics2d-1.0.0.jar", "c:\\MD\\MagicDraw\\lib\\freehep-graphicsio-1.0.0.jar", "c:\\MD\\MagicDraw\\lib\\freehep-graphicsio-emf-1.0.0.jar", "c:\\MD\\MagicDraw\\lib\\freehep-graphicsio-ps-1.0.0.jar", "c:\\MD\\MagicDraw\\lib\\gson-2.9.0.jar", "c:\\MD\\MagicDraw\\lib\\guava-31.0.1-jre.jar", "c:\\MD\\MagicDraw\\lib\\HTMLEditorLight-21.jar", "c:\\MD\\MagicDraw\\lib\\httpclient-4.5.13.jar", "c:\\MD\\MagicDraw\\lib\\httpcore-4.4.13.jar", "c:\\MD\\MagicDraw\\lib\\httpmime-4.5.13.jar", "c:\\MD\\MagicDraw\\lib\\j2objc-annotations-1.3.jar", "c:\\MD\\MagicDraw\\lib\\jackson-annotations-2.13.2.jar", "c:\\MD\\MagicDraw\\lib\\jackson-core-2.13.2.jar", "c:\\MD\\MagicDraw\\lib\\jackson-core-asl-1.9.2.jar", "c:\\MD\\MagicDraw\\lib\\jackson-databind-2.13.2.1.jar", "c:\\MD\\MagicDraw\\lib\\jakarta.activation-1.2.1.jar", "c:\\MD\\MagicDraw\\lib\\jakarta.activation-api-1.2.1.jar", "c:\\MD\\MagicDraw\\lib\\jakarta.xml.bind-api-2.3.2.jar", "c:\\MD\\MagicDraw\\lib\\javassist-3.28.0-GA.jar", "c:\\MD\\MagicDraw\\lib\\javax.jmi-1.0.jar", "c:\\MD\\MagicDraw\\lib\\jaxb-api-2.2.11.jar", "c:\\MD\\MagicDraw\\lib\\jaxb-core-2.3.0.jar", "c:\\MD\\MagicDraw\\lib\\jaxb-impl-2.3.2.jar", "c:\\MD\\MagicDraw\\lib\\jhall-2.0_03.jar", "c:\\MD\\MagicDraw\\lib\\jide-action-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jide-charts-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jide-common-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jide-components-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jide-diff-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jide-dock-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jide-editor-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jide-gantt-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jide-grids-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jide-shortcut-3.7.13.jar", "c:\\MD\\MagicDraw\\lib\\jimi-1.0.jar", "c:\\MD\\MagicDraw\\lib\\jna-4.3.0.jar", "c:\\MD\\MagicDraw\\lib\\joda-time-2.9.9.jar", "c:\\MD\\MagicDraw\\lib\\json-20200518.jar", "c:\\MD\\MagicDraw\\lib\\jsr250-api-1.0.jar", "c:\\MD\\MagicDraw\\lib\\jsr305-2.0.1.jar", "c:\\MD\\MagicDraw\\lib\\jtidy-r938.jar", "c:\\MD\\MagicDraw\\lib\\junit-4.13.2.jar", "c:\\MD\\MagicDraw\\lib\\junit-jupiter-api-5.9.0.jar", "c:\\MD\\MagicDraw\\lib\\JUnitParams-1.0.1.jar", "c:\\MD\\MagicDraw\\lib\\jxbrowser-7.19.jar", "c:\\MD\\MagicDraw\\lib\\jxbrowser-linux64-7.19.jar", "c:\\MD\\MagicDraw\\lib\\jxbrowser-linux64-arm-7.19.jar", "c:\\MD\\MagicDraw\\lib\\jxbrowser-mac-7.19.jar", "c:\\MD\\MagicDraw\\lib\\jxbrowser-mac-arm-7.19.jar", "c:\\MD\\MagicDraw\\lib\\jxbrowser-swing-7.19.jar", "c:\\MD\\MagicDraw\\lib\\jxbrowser-win64-7.19.jar", "c:\\MD\\MagicDraw\\lib\\kryo-4.0.2.jar", "c:\\MD\\MagicDraw\\lib\\launcher.jar", "c:\\MD\\MagicDraw\\lib\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar", "c:\\MD\\MagicDraw\\lib\\log4j-1.2-api-2.17.1.jar", "c:\\MD\\MagicDraw\\lib\\log4j-api-2.17.1.jar", "c:\\MD\\MagicDraw\\lib\\log4j-core-2.17.1.jar", "c:\\MD\\MagicDraw\\lib\\log4j-slf4j-impl-2.17.1.jar", "c:\\MD\\MagicDraw\\lib\\lucene-analysis-common-9.2.0.jar", "c:\\MD\\MagicDraw\\lib\\lucene-core-9.2.0.jar", "c:\\MD\\MagicDraw\\lib\\lucene-misc-9.2.0.jar", "c:\\MD\\MagicDraw\\lib\\lz4-java-1.8.0.jar", "c:\\MD\\MagicDraw\\lib\\md.jar", "c:\\MD\\MagicDraw\\lib\\md_api.jar", "c:\\MD\\MagicDraw\\lib\\md-flexlm-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\md-yfiles-2022.1.0-0-SNAPSHOT.jar", "c:\\MD\\MagicDraw\\lib\\metrics-core-3.2.2.jar", "c:\\MD\\MagicDraw\\lib\\metrics-graphite-3.2.2.jar", "c:\\MD\\MagicDraw\\lib\\metrics-jmx-4.2.7.jar", "c:\\MD\\MagicDraw\\lib\\minlog-1.3.0.jar", "c:\\MD\\MagicDraw\\lib\\netty-buffer-4.1.79.Final.jar", "c:\\MD\\MagicDraw\\lib\\netty-codec-4.1.79.Final.jar", "c:\\MD\\MagicDraw\\lib\\netty-codec-http-4.1.79.Final.jar", "c:\\MD\\MagicDraw\\lib\\netty-common-4.1.79.Final.jar", "c:\\MD\\MagicDraw\\lib\\netty-handler-4.1.79.Final.jar", "c:\\MD\\MagicDraw\\lib\\netty-resolver-4.1.79.Final.jar", "c:\\MD\\MagicDraw\\lib\\netty-tcnative-classes-2.0.49.Final.jar", "c:\\MD\\MagicDraw\\lib\\netty-transport-4.1.79.Final.jar", "c:\\MD\\MagicDraw\\lib\\netty-transport-native-unix-common-4.1.79.Final.jar", "c:\\MD\\MagicDraw\\lib\\objenesis-2.6.jar", "c:\\MD\\MagicDraw\\lib\\org.apache.felix.scr-2.0.10.jar", "c:\\MD\\MagicDraw\\lib\\org.eclipse.emf.common-2.25.0.jar", "c:\\MD\\MagicDraw\\lib\\org.eclipse.emf.ecore.xmi-2.16.0.jar", "c:\\MD\\MagicDraw\\lib\\org.eclipse.emf.ecore-2.27.0.jar", "c:\\MD\\MagicDraw\\lib\\org.eclipse.uml2.common-1.5.0.v201005031530.jar", "c:\\MD\\MagicDraw\\lib\\org.osgi.annotation.versioning-1.1.0.jar", "c:\\MD\\MagicDraw\\lib\\patch.jar", "c:\\MD\\MagicDraw\\lib\\poi-5.2.2.jar", "c:\\MD\\MagicDraw\\lib\\poi-ooxml-5.2.2.jar", "c:\\MD\\MagicDraw\\lib\\poi-ooxml-full-5.2.2.jar", "c:\\MD\\MagicDraw\\lib\\reflectasm-1.11.3.jar", "c:\\MD\\MagicDraw\\lib\\scala-java8-compat_2.13-1.0.0.jar", "c:\\MD\\MagicDraw\\lib\\scala-library-2.13.7.jar", "c:\\MD\\MagicDraw\\lib\\serializer-2.7.2.jar", "c:\\MD\\MagicDraw\\lib\\slf4j-api-1.7.25.jar", "c:\\MD\\MagicDraw\\lib\\SparseBitSet-1.2.jar", "c:\\MD\\MagicDraw\\lib\\supercsv-1.52.jar", "c:\\MD\\MagicDraw\\lib\\tas-1.4.jar", "c:\\MD\\MagicDraw\\lib\\trove4j-3.0.3.jar", "c:\\MD\\MagicDraw\\lib\\truezip-driver-zip-7.7.10.jar", "c:\\MD\\MagicDraw\\lib\\truezip-kernel-7.7.10.jar", "c:\\MD\\MagicDraw\\lib\\truezip-swing-7.7.10.jar", "c:\\MD\\MagicDraw\\lib\\xalan-2.7.2.jar", "c:\\MD\\MagicDraw\\lib\\xercesImpl-2.12.2.jar", "c:\\MD\\MagicDraw\\lib\\xml-apis-ext-1.3.04.jar", "c:\\MD\\MagicDraw\\lib\\xmlbeans-5.0.3.jar", "c:\\MD\\MagicDraw\\lib\\xmlgraphics-commons-2.6.jar"], "java.compile.nullAnalysis.mode": "automatic"}